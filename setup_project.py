#!/usr/bin/env python3
"""
LuxEstate Django Backend Setup Script
This script sets up the complete Django backend for the luxury real estate website.
"""

import os
import sys
import subprocess
import platform

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully!")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during {description}:")
        print(f"Command: {command}")
        print(f"Error: {e.stderr}")
        return None

def setup_virtual_environment():
    """Set up Python virtual environment."""
    print("🚀 Setting up LuxEstate Django Backend...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python 3.8 or higher is required!")
        sys.exit(1)
    
    print(f"✅ Python {python_version.major}.{python_version.minor} detected")
    
    # Create virtual environment
    venv_name = "luxestate_env"
    
    if os.path.exists(venv_name):
        print(f"📁 Virtual environment '{venv_name}' already exists")
    else:
        run_command(f"python -m venv {venv_name}", f"Creating virtual environment '{venv_name}'")
    
    # Determine activation command based on OS
    if platform.system() == "Windows":
        activate_cmd = f"{venv_name}\\Scripts\\activate"
        pip_cmd = f"{venv_name}\\Scripts\\pip"
        python_cmd = f"{venv_name}\\Scripts\\python"
    else:
        activate_cmd = f"source {venv_name}/bin/activate"
        pip_cmd = f"{venv_name}/bin/pip"
        python_cmd = f"{venv_name}/bin/python"
    
    # Install requirements
    run_command(f"{pip_cmd} install --upgrade pip", "Upgrading pip")
    run_command(f"{pip_cmd} install -r requirements.txt", "Installing Django and dependencies")
    
    return python_cmd, activate_cmd

def create_django_project(python_cmd):
    """Create Django project structure."""
    project_name = "luxestate_backend"
    
    if os.path.exists(project_name):
        print(f"📁 Django project '{project_name}' already exists")
    else:
        run_command(f"{python_cmd} -m django startproject {project_name} .", f"Creating Django project '{project_name}'")
    
    # Create apps
    apps = ['properties', 'users', 'messages', 'analytics', 'payments']
    
    for app in apps:
        app_path = f"{project_name}/{app}"
        if os.path.exists(app_path):
            print(f"📁 App '{app}' already exists")
        else:
            run_command(f"{python_cmd} manage.py startapp {app}", f"Creating app '{app}'")

def main():
    """Main setup function."""
    print("🏠 LuxEstate - Luxury Real Estate Backend Setup")
    print("=" * 50)
    
    # Setup virtual environment
    python_cmd, activate_cmd = setup_virtual_environment()
    
    # Create Django project
    create_django_project(python_cmd)
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print(f"1. Activate virtual environment: {activate_cmd}")
    print("2. Run: python manage.py migrate")
    print("3. Run: python manage.py createsuperuser")
    print("4. Run: python manage.py runserver")
    print("\n🌐 Your Django backend will be available at: http://127.0.0.1:8000/")

if __name__ == "__main__":
    main()
