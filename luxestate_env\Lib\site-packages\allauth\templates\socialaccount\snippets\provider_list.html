{% load socialaccount %}

{% get_providers as socialaccount_providers %}

{% for provider in socialaccount_providers %}
{% if provider.id == "openid" %}
{% for brand in provider.get_brands %}
<li>
  <a title="{{brand.name}}"
     class="socialaccount_provider {{provider.id}} {{brand.id}}"
     href="{% provider_login_url provider openid=brand.openid_url process=process %}"
     >{{brand.name}}</a>
</li>
{% endfor %}
{% endif %}
<li>
  <a title="{{provider.name}}" class="socialaccount_provider {{provider.id}}"
     href="{% provider_login_url provider process=process scope=scope auth_params=auth_params %}">{{provider.name}}</a>
</li>
{% endfor %}
