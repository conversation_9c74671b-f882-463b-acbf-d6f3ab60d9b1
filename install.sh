#!/bin/bash

# LuxEstate Django Backend Installation Script
# This script sets up the complete Django backend with virtual environment

set -e  # Exit on any error

echo "🏠 LuxEstate - Luxury Real Estate Backend Installation"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python 3.8+ is installed
check_python() {
    print_status "Checking Python version..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
        
        if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 8 ]; then
            print_success "Python $PYTHON_VERSION detected"
            PYTHON_CMD="python3"
        else
            print_error "Python 3.8 or higher is required. Found: $PYTHON_VERSION"
            exit 1
        fi
    else
        print_error "Python 3 is not installed. Please install Python 3.8 or higher."
        exit 1
    fi
}

# Create virtual environment
create_venv() {
    print_status "Creating virtual environment..."
    
    VENV_NAME="luxestate_env"
    
    if [ -d "$VENV_NAME" ]; then
        print_warning "Virtual environment '$VENV_NAME' already exists"
        read -p "Do you want to recreate it? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$VENV_NAME"
            $PYTHON_CMD -m venv "$VENV_NAME"
            print_success "Virtual environment recreated"
        else
            print_status "Using existing virtual environment"
        fi
    else
        $PYTHON_CMD -m venv "$VENV_NAME"
        print_success "Virtual environment created"
    fi
    
    # Activate virtual environment
    source "$VENV_NAME/bin/activate"
    print_success "Virtual environment activated"
}

# Install dependencies
install_dependencies() {
    print_status "Installing Python dependencies..."
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install requirements
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        print_success "Dependencies installed successfully"
    else
        print_error "requirements.txt not found"
        exit 1
    fi
}

# Setup Django project
setup_django() {
    print_status "Setting up Django project..."
    
    # Create .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        print_status "Creating .env file..."
        cat > .env << EOF
# Django Settings
SECRET_KEY=django-insecure-$(openssl rand -base64 32)
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (SQLite for development)
DB_ENGINE=django.db.backends.sqlite3
DB_NAME=db.sqlite3

# Email Settings (Console backend for development)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# Celery Settings (Optional - for background tasks)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Application Settings
PROPERTY_IMAGE_MAX_SIZE=5242880
PROPERTY_MAX_IMAGES=20
EOF
        print_success ".env file created"
    else
        print_warning ".env file already exists"
    fi
    
    # Run migrations
    print_status "Running database migrations..."
    python manage.py makemigrations
    python manage.py migrate
    print_success "Database migrations completed"
    
    # Collect static files
    print_status "Collecting static files..."
    python manage.py collectstatic --noinput
    print_success "Static files collected"
}

# Create superuser
create_superuser() {
    print_status "Creating superuser account..."
    
    echo "Please create a superuser account for Django admin:"
    python manage.py createsuperuser
    print_success "Superuser created"
}

# Copy frontend files
copy_frontend() {
    print_status "Setting up frontend files..."
    
    # Create templates directory
    mkdir -p templates
    
    # Copy HTML files to templates
    if [ -f "index.html" ]; then
        cp *.html templates/ 2>/dev/null || true
        print_success "HTML files copied to templates/"
    fi
    
    # Create static directory structure
    mkdir -p static/assets/{css,js,images}
    
    # Copy assets if they exist
    if [ -d "assets" ]; then
        cp -r assets/* static/assets/ 2>/dev/null || true
        print_success "Assets copied to static/"
    fi
}

# Install Redis (optional)
install_redis() {
    print_status "Checking Redis installation..."
    
    if command -v redis-server &> /dev/null; then
        print_success "Redis is already installed"
    else
        print_warning "Redis is not installed. It's required for Celery and WebSocket support."
        echo "Please install Redis:"
        echo "  Ubuntu/Debian: sudo apt-get install redis-server"
        echo "  macOS: brew install redis"
        echo "  Windows: Download from https://redis.io/download"
    fi
}

# Main installation process
main() {
    echo
    print_status "Starting LuxEstate backend installation..."
    echo
    
    check_python
    create_venv
    install_dependencies
    setup_django
    copy_frontend
    install_redis
    
    echo
    print_status "Creating superuser..."
    create_superuser
    
    echo
    print_success "🎉 Installation completed successfully!"
    echo
    echo "📋 Next steps:"
    echo "1. Activate virtual environment: source luxestate_env/bin/activate"
    echo "2. Start development server: python manage.py runserver"
    echo "3. Visit: http://127.0.0.1:8000/"
    echo "4. Admin panel: http://127.0.0.1:8000/admin/"
    echo "5. API documentation: http://127.0.0.1:8000/api/"
    echo
    echo "🔧 Optional services:"
    echo "- Start Redis: redis-server"
    echo "- Start Celery worker: celery -A luxestate_backend worker -l info"
    echo "- Start Celery beat: celery -A luxestate_backend beat -l info"
    echo
    print_success "Happy coding! 🚀"
}

# Run main function
main
