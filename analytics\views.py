"""
Views for the analytics app.
"""

from rest_framework import viewsets, permissions, status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.db.models import Count, Avg, Sum, Q
from django.utils import timezone
from datetime import timedelta, datetime
from django.contrib.auth import get_user_model

from .models import PropertyAnalytics, UserAnalytics, SearchAnalytics, MarketTrends, PerformanceMetrics

User = get_user_model()

class PropertyAnalyticsViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for property analytics."""
    
    queryset = PropertyAnalytics.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter analytics based on user permissions."""
        if self.request.user.is_staff:
            return self.queryset
        
        # Users can only see analytics for their own properties
        return self.queryset.filter(property__owner=self.request.user)

class UserAnalyticsViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for user analytics."""
    
    queryset = UserAnalytics.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter analytics based on user permissions."""
        if self.request.user.is_staff:
            return self.queryset
        
        # Users can only see their own analytics
        return self.queryset.filter(user=self.request.user)

class SearchAnalyticsViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for search analytics."""
    
    queryset = SearchAnalytics.objects.all()
    permission_classes = [permissions.IsAdminUser]

class MarketTrendsViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for market trends."""
    
    queryset = MarketTrends.objects.all()
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

class DashboardAnalyticsView(APIView):
    """Dashboard analytics overview."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get dashboard analytics data."""
        user = request.user
        today = timezone.now().date()
        last_30_days = today - timedelta(days=30)
        
        # Property analytics for user's properties
        user_properties = user.owned_properties.filter(status='active')
        
        # Basic stats
        stats = {
            'total_properties': user_properties.count(),
            'active_properties': user_properties.filter(status='active').count(),
            'total_views': user_properties.aggregate(total=Sum('views_count'))['total'] or 0,
            'total_inquiries': user_properties.aggregate(total=Sum('inquiries_count'))['total'] or 0,
            'total_favorites': user_properties.aggregate(total=Sum('favorites_count'))['total'] or 0,
        }
        
        # Recent analytics (last 30 days)
        recent_analytics = PropertyAnalytics.objects.filter(
            property__owner=user,
            date__gte=last_30_days
        ).aggregate(
            recent_views=Sum('views'),
            recent_inquiries=Sum('inquiries'),
            recent_favorites=Sum('favorites')
        )
        
        stats.update({
            'recent_views': recent_analytics['recent_views'] or 0,
            'recent_inquiries': recent_analytics['recent_inquiries'] or 0,
            'recent_favorites': recent_analytics['recent_favorites'] or 0,
        })
        
        # Top performing properties
        top_properties = user_properties.order_by('-views_count')[:5].values(
            'id', 'title', 'views_count', 'inquiries_count', 'favorites_count'
        )
        
        # Daily views for the last 30 days
        daily_views = []
        for i in range(30):
            date = today - timedelta(days=i)
            views = PropertyAnalytics.objects.filter(
                property__owner=user,
                date=date
            ).aggregate(total=Sum('views'))['total'] or 0
            
            daily_views.append({
                'date': date.isoformat(),
                'views': views
            })
        
        daily_views.reverse()  # Show oldest to newest
        
        # Property type distribution
        property_types = user_properties.values('property_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # Listing type distribution
        listing_types = user_properties.values('listing_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        return Response({
            'stats': stats,
            'top_properties': list(top_properties),
            'daily_views': daily_views,
            'property_types': list(property_types),
            'listing_types': list(listing_types),
        })

class ReportsView(APIView):
    """Generate various reports."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get available reports."""
        reports = [
            {
                'id': 'property_performance',
                'name': 'Property Performance Report',
                'description': 'Detailed analytics for your properties'
            },
            {
                'id': 'market_trends',
                'name': 'Market Trends Report',
                'description': 'Market analysis and trends'
            },
            {
                'id': 'user_activity',
                'name': 'User Activity Report',
                'description': 'Your activity and engagement metrics'
            }
        ]
        
        return Response({'reports': reports})
    
    def post(self, request):
        """Generate a specific report."""
        report_type = request.data.get('report_type')
        date_from = request.data.get('date_from')
        date_to = request.data.get('date_to')
        
        if not report_type:
            return Response({'error': 'Report type is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Parse dates
        try:
            if date_from:
                date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            else:
                date_from = timezone.now().date() - timedelta(days=30)
            
            if date_to:
                date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
            else:
                date_to = timezone.now().date()
        except ValueError:
            return Response({'error': 'Invalid date format'}, status=status.HTTP_400_BAD_REQUEST)
        
        if report_type == 'property_performance':
            return self.generate_property_performance_report(request.user, date_from, date_to)
        elif report_type == 'market_trends':
            return self.generate_market_trends_report(date_from, date_to)
        elif report_type == 'user_activity':
            return self.generate_user_activity_report(request.user, date_from, date_to)
        else:
            return Response({'error': 'Invalid report type'}, status=status.HTTP_400_BAD_REQUEST)
    
    def generate_property_performance_report(self, user, date_from, date_to):
        """Generate property performance report."""
        properties = user.owned_properties.all()
        
        report_data = []
        for property in properties:
            analytics = PropertyAnalytics.objects.filter(
                property=property,
                date__range=[date_from, date_to]
            ).aggregate(
                total_views=Sum('views'),
                total_inquiries=Sum('inquiries'),
                total_favorites=Sum('favorites'),
                avg_time_on_page=Avg('average_time_on_page')
            )
            
            report_data.append({
                'property_id': property.id,
                'property_title': property.title,
                'property_type': property.property_type,
                'listing_type': property.listing_type,
                'price': str(property.price),
                'total_views': analytics['total_views'] or 0,
                'total_inquiries': analytics['total_inquiries'] or 0,
                'total_favorites': analytics['total_favorites'] or 0,
                'avg_time_on_page': str(analytics['avg_time_on_page'] or timedelta(0)),
                'conversion_rate': (analytics['total_inquiries'] or 0) / max(analytics['total_views'] or 1, 1) * 100
            })
        
        return Response({
            'report_type': 'property_performance',
            'date_range': {'from': date_from, 'to': date_to},
            'data': report_data
        })
    
    def generate_market_trends_report(self, date_from, date_to):
        """Generate market trends report."""
        trends = MarketTrends.objects.filter(
            period_start__gte=date_from,
            period_end__lte=date_to
        ).order_by('-created_at')
        
        report_data = []
        for trend in trends:
            report_data.append({
                'trend_type': trend.trend_type,
                'location': trend.location,
                'property_type': trend.property_type,
                'current_value': str(trend.current_value),
                'previous_value': str(trend.previous_value),
                'percentage_change': str(trend.percentage_change),
                'period_start': trend.period_start,
                'period_end': trend.period_end
            })
        
        return Response({
            'report_type': 'market_trends',
            'date_range': {'from': date_from, 'to': date_to},
            'data': report_data
        })
    
    def generate_user_activity_report(self, user, date_from, date_to):
        """Generate user activity report."""
        analytics = UserAnalytics.objects.filter(
            user=user,
            date__range=[date_from, date_to]
        ).aggregate(
            total_properties_viewed=Sum('properties_viewed'),
            total_properties_favorited=Sum('properties_favorited'),
            total_messages_sent=Sum('messages_sent'),
            total_messages_received=Sum('messages_received'),
            total_inquiries_sent=Sum('inquiries_sent'),
            total_inquiries_received=Sum('inquiries_received'),
            total_sessions=Sum('sessions'),
            avg_session_duration=Avg('session_duration')
        )
        
        return Response({
            'report_type': 'user_activity',
            'date_range': {'from': date_from, 'to': date_to},
            'data': {
                'properties_viewed': analytics['total_properties_viewed'] or 0,
                'properties_favorited': analytics['total_properties_favorited'] or 0,
                'messages_sent': analytics['total_messages_sent'] or 0,
                'messages_received': analytics['total_messages_received'] or 0,
                'inquiries_sent': analytics['total_inquiries_sent'] or 0,
                'inquiries_received': analytics['total_inquiries_received'] or 0,
                'total_sessions': analytics['total_sessions'] or 0,
                'avg_session_duration': str(analytics['avg_session_duration'] or timedelta(0))
            }
        })
