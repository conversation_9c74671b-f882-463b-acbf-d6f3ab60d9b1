<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - LuxEstate</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    
    <!-- AOS CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <style>
        /* Dashboard Specific Styles */
        .dashboard-sidebar {
            background: var(--bs-secondary-bg);
            border-right: 1px solid var(--bs-border-color);
            min-height: calc(100vh - 76px);
            position: sticky;
            top: 76px;
        }
        
        .dashboard-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .dashboard-nav-item {
            margin-bottom: 0.5rem;
        }
        
        .dashboard-nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: var(--bs-body-color);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
        }
        
        .dashboard-nav-link:hover,
        .dashboard-nav-link.active {
            background: var(--luxury-gold);
            color: var(--luxury-dark);
        }
        
        .dashboard-nav-link i {
            font-size: 1.1rem;
            width: 20px;
        }
        
        .dashboard-content {
            padding: 2rem;
        }
        
        .dashboard-header {
            background: var(--bs-body-bg);
            border-bottom: 1px solid var(--bs-border-color);
            padding: 1.5rem 2rem;
            margin: -2rem -2rem 2rem -2rem;
        }
        
        .stat-card {
            background: var(--bs-body-bg);
            border: 1px solid var(--bs-border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            transition: var(--transition);
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
        }
        
        .stat-icon.primary {
            background: rgba(255, 193, 7, 0.1);
            color: var(--luxury-gold);
        }
        
        .stat-icon.success {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }
        
        .stat-icon.info {
            background: rgba(23, 162, 184, 0.1);
            color: #17a2b8;
        }
        
        .stat-icon.warning {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }
        
        .property-table {
            background: var(--bs-body-bg);
            border: 1px solid var(--bs-border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
        }
        
        .property-table .table {
            margin-bottom: 0;
        }
        
        .property-table .table th {
            background: var(--bs-secondary-bg);
            border-bottom: 1px solid var(--bs-border-color);
            font-weight: 600;
            color: var(--bs-emphasis-color);
        }
        
        .property-image-thumb {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: var(--border-radius);
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-badge.active {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }
        
        .status-badge.pending {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }
        
        .status-badge.sold {
            background: rgba(108, 117, 125, 0.1);
            color: #6c757d;
        }
        
        .dashboard-tabs .nav-link {
            color: var(--bs-body-color);
            border: none;
            border-bottom: 2px solid transparent;
            border-radius: 0;
            padding: 1rem 1.5rem;
            font-weight: 600;
        }
        
        .dashboard-tabs .nav-link.active {
            color: var(--luxury-gold);
            border-bottom-color: var(--luxury-gold);
            background: transparent;
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--luxury-gold);
        }
        
        .activity-item {
            display: flex;
            align-items: start;
            gap: 1rem;
            padding: 1rem;
            border-bottom: 1px solid var(--bs-border-color);
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--bs-secondary-bg);
            color: var(--luxury-gold);
            flex-shrink: 0;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-time {
            font-size: 0.9rem;
            color: var(--bs-secondary-color);
        }
        
        @media (max-width: 768px) {
            .dashboard-sidebar {
                position: static;
                min-height: auto;
            }
            
            .dashboard-content {
                padding: 1rem;
            }
            
            .dashboard-header {
                margin: -1rem -1rem 1rem -1rem;
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top luxury-nav">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold fs-3" href="index.html">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </a>
            
            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Desktop Navigation -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="listings.html">Properties</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">Contact</a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <!-- Theme Switcher -->
                    <div class="dropdown me-3">
                        <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-palette"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item theme-option" href="#" data-theme="light">
                                <i class="bi bi-sun"></i> Light
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="dark">
                                <i class="bi bi-moon"></i> Dark
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="luxury">
                                <i class="bi bi-gem"></i> Luxury
                            </a></li>
                        </ul>
                    </div>
                    
                    <!-- User Dropdown -->
                    <div class="dropdown">
                        <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i> John Doe
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-person me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="login.html"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Navigation Offcanvas -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="mobileNav">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="index.html">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="listings.html">Properties</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="about.html">About</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="contact.html">Contact</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="favorites.html">Favorites</a>
                </li>
            </ul>
            <hr>
            <div class="d-grid gap-2">
                <a href="add-property.html" class="btn btn-warning">Add Property</a>
                <a href="login.html" class="btn btn-outline-warning">Logout</a>
            </div>
        </div>
    </div>

    <!-- Dashboard Layout -->
    <div class="container-fluid mt-5 pt-3">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-xl-2 p-0">
                <div class="dashboard-sidebar p-3">
                    <ul class="dashboard-nav">
                        <li class="dashboard-nav-item">
                            <a href="#overview" class="dashboard-nav-link active" data-tab="overview">
                                <i class="bi bi-speedometer2"></i>
                                <span>Overview</span>
                            </a>
                        </li>
                        <li class="dashboard-nav-item">
                            <a href="#my-properties" class="dashboard-nav-link" data-tab="my-properties">
                                <i class="bi bi-house"></i>
                                <span>My Properties</span>
                            </a>
                        </li>
                        <li class="dashboard-nav-item">
                            <a href="#favorites" class="dashboard-nav-link" data-tab="favorites">
                                <i class="bi bi-heart"></i>
                                <span>Favorites</span>
                            </a>
                        </li>
                        <li class="dashboard-nav-item">
                            <a href="#messages" class="dashboard-nav-link" data-tab="messages">
                                <i class="bi bi-envelope"></i>
                                <span>Messages</span>
                                <span class="badge bg-warning text-dark ms-auto">3</span>
                            </a>
                        </li>
                        <li class="dashboard-nav-item">
                            <a href="#analytics" class="dashboard-nav-link" data-tab="analytics">
                                <i class="bi bi-graph-up"></i>
                                <span>Analytics</span>
                            </a>
                        </li>
                        <li class="dashboard-nav-item">
                            <a href="#settings" class="dashboard-nav-link" data-tab="settings">
                                <i class="bi bi-gear"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="my-4">
                    
                    <div class="d-grid gap-2">
                        <a href="add-property.html" class="btn btn-warning">
                            <i class="bi bi-plus-circle me-2"></i>Add Property
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-lg-9 col-xl-10">
                <div class="dashboard-content">
                    <!-- Overview Tab -->
                    <div class="tab-content active" id="overview-content">
                        <div class="dashboard-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h2 class="fw-bold mb-1">Welcome back, John!</h2>
                                    <p class="text-muted mb-0">Here's what's happening with your properties today.</p>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">Last updated: Today, 2:30 PM</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Stats Cards -->
                        <div class="row g-4 mb-4">
                            <div class="col-md-6 col-xl-3">
                                <div class="stat-card">
                                    <div class="stat-icon primary">
                                        <i class="bi bi-house"></i>
                                    </div>
                                    <h3 class="fw-bold mb-1">12</h3>
                                    <p class="text-muted mb-0">Total Properties</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-xl-3">
                                <div class="stat-card">
                                    <div class="stat-icon success">
                                        <i class="bi bi-check-circle"></i>
                                    </div>
                                    <h3 class="fw-bold mb-1">8</h3>
                                    <p class="text-muted mb-0">Active Listings</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-xl-3">
                                <div class="stat-card">
                                    <div class="stat-icon info">
                                        <i class="bi bi-eye"></i>
                                    </div>
                                    <h3 class="fw-bold mb-1">1,247</h3>
                                    <p class="text-muted mb-0">Total Views</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-xl-3">
                                <div class="stat-card">
                                    <div class="stat-icon warning">
                                        <i class="bi bi-calendar-check"></i>
                                    </div>
                                    <h3 class="fw-bold mb-1">23</h3>
                                    <p class="text-muted mb-0">Scheduled Viewings</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Recent Properties -->
                            <div class="col-lg-8 mb-4">
                                <div class="property-table">
                                    <div class="p-3 border-bottom">
                                        <h5 class="fw-bold mb-0">Recent Properties</h5>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>Property</th>
                                                    <th>Price</th>
                                                    <th>Status</th>
                                                    <th>Views</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="recentPropertiesTable">
                                                <!-- Properties will be loaded dynamically -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Recent Activity -->
                            <div class="col-lg-4 mb-4">
                                <div class="property-table">
                                    <div class="p-3 border-bottom">
                                        <h5 class="fw-bold mb-0">Recent Activity</h5>
                                    </div>
                                    <div id="recentActivity">
                                        <!-- Activity items will be loaded dynamically -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Other tab contents will be added here -->
                    <div class="tab-content" id="my-properties-content" style="display: none;">
                        <div class="dashboard-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h2 class="fw-bold mb-0">My Properties</h2>
                                <a href="add-property.html" class="btn btn-warning">
                                    <i class="bi bi-plus-circle me-2"></i>Add New Property
                                </a>
                            </div>
                        </div>
                        
                        <div class="property-table">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Property</th>
                                            <th>Location</th>
                                            <th>Price</th>
                                            <th>Status</th>
                                            <th>Views</th>
                                            <th>Listed Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="myPropertiesTable">
                                        <!-- Properties will be loaded dynamically -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional tab contents would continue here... -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- AOS JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/property-data.js"></script>
    <script src="assets/js/theme-switcher.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/dashboard.js"></script>
</body>
</html>
