"""
URLs for the analytics app.
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'property-analytics', views.PropertyAnalyticsViewSet, basename='property-analytics')
router.register(r'user-analytics', views.UserAnalyticsViewSet, basename='user-analytics')
router.register(r'search-analytics', views.SearchAnalyticsViewSet, basename='search-analytics')
router.register(r'market-trends', views.MarketTrendsViewSet, basename='market-trends')

urlpatterns = [
    path('', include(router.urls)),
    path('dashboard/', views.DashboardAnalyticsView.as_view(), name='dashboard-analytics'),
    path('reports/', views.ReportsView.as_view(), name='reports'),
]
