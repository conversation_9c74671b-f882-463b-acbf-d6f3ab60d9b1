// ===== THEME SWITCHER FUNCTIONALITY =====

// Theme management
class ThemeSwitcher {
    constructor() {
        this.currentTheme = this.getStoredTheme() || 'light';
        this.init();
    }

    init() {
        this.setTheme(this.currentTheme);
        this.bindEvents();
        this.updateThemeIcon();
    }

    bindEvents() {
        // Theme option clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.theme-option')) {
                e.preventDefault();
                const theme = e.target.closest('.theme-option').getAttribute('data-theme');
                this.setTheme(theme);
            }
        });

        // Keyboard shortcut for theme switching (Ctrl/Cmd + Shift + T)
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                this.cycleTheme();
            }
        });

        // Listen for system theme changes
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (this.currentTheme === 'auto') {
                    this.applySystemTheme();
                }
            });
        }
    }

    setTheme(theme) {
        this.currentTheme = theme;
        this.storeTheme(theme);
        this.applyTheme(theme);
        this.updateThemeIcon();
        this.announceThemeChange(theme);
    }

    applyTheme(theme) {
        const html = document.documentElement;
        
        // Remove existing theme classes
        html.classList.remove('theme-light', 'theme-dark', 'theme-luxury');
        
        // Apply new theme
        switch (theme) {
            case 'dark':
                html.setAttribute('data-bs-theme', 'dark');
                html.classList.add('theme-dark');
                break;
            case 'luxury':
                html.setAttribute('data-bs-theme', 'luxury');
                html.classList.add('theme-luxury');
                break;
            case 'auto':
                this.applySystemTheme();
                break;
            default: // light
                html.setAttribute('data-bs-theme', 'light');
                html.classList.add('theme-light');
                break;
        }

        // Update meta theme-color for mobile browsers
        this.updateMetaThemeColor(theme);
        
        // Trigger custom event for other components
        window.dispatchEvent(new CustomEvent('themeChanged', { 
            detail: { theme: theme } 
        }));
    }

    applySystemTheme() {
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        const systemTheme = prefersDark ? 'dark' : 'light';
        
        const html = document.documentElement;
        html.setAttribute('data-bs-theme', systemTheme);
        html.classList.remove('theme-light', 'theme-dark', 'theme-luxury');
        html.classList.add(`theme-${systemTheme}`);
        
        this.updateMetaThemeColor(systemTheme);
    }

    updateMetaThemeColor(theme) {
        let themeColor;
        switch (theme) {
            case 'dark':
                themeColor = '#1a1a1a';
                break;
            case 'luxury':
                themeColor = '#0a0a0a';
                break;
            case 'auto':
                themeColor = window.matchMedia('(prefers-color-scheme: dark)').matches ? '#1a1a1a' : '#ffffff';
                break;
            default: // light
                themeColor = '#ffffff';
                break;
        }

        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        metaThemeColor.content = themeColor;
    }

    cycleTheme() {
        const themes = ['light', 'dark', 'luxury'];
        const currentIndex = themes.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themes.length;
        this.setTheme(themes[nextIndex]);
    }

    updateThemeIcon() {
        const themeButtons = document.querySelectorAll('.theme-toggle');
        const themeOptions = document.querySelectorAll('.theme-option');
        
        // Update theme toggle buttons
        themeButtons.forEach(button => {
            const icon = button.querySelector('i');
            if (icon) {
                icon.className = this.getThemeIcon(this.currentTheme);
            }
        });

        // Update active state in dropdown
        themeOptions.forEach(option => {
            const theme = option.getAttribute('data-theme');
            if (theme === this.currentTheme) {
                option.classList.add('active');
            } else {
                option.classList.remove('active');
            }
        });
    }

    getThemeIcon(theme) {
        switch (theme) {
            case 'dark':
                return 'bi bi-moon-fill';
            case 'luxury':
                return 'bi bi-gem';
            case 'auto':
                return 'bi bi-circle-half';
            default: // light
                return 'bi bi-sun-fill';
        }
    }

    announceThemeChange(theme) {
        const themeNames = {
            light: 'Light Theme',
            dark: 'Dark Theme',
            luxury: 'Luxury Theme',
            auto: 'Auto Theme'
        };

        // Show toast notification if available
        if (window.LuxEstate && window.LuxEstate.showToast) {
            window.LuxEstate.showToast(
                'Theme Changed', 
                `Switched to ${themeNames[theme]}`, 
                'info'
            );
        }

        // Announce to screen readers
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        announcement.textContent = `Theme changed to ${themeNames[theme]}`;
        document.body.appendChild(announcement);
        
        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }

    storeTheme(theme) {
        try {
            localStorage.setItem('luxestate-theme', theme);
        } catch (e) {
            console.warn('Could not store theme preference:', e);
        }
    }

    getStoredTheme() {
        try {
            return localStorage.getItem('luxestate-theme');
        } catch (e) {
            console.warn('Could not retrieve stored theme:', e);
            return null;
        }
    }

    // Public methods for external use
    getCurrentTheme() {
        return this.currentTheme;
    }

    getAvailableThemes() {
        return [
            { value: 'light', name: 'Light', icon: 'bi bi-sun' },
            { value: 'dark', name: 'Dark', icon: 'bi bi-moon' },
            { value: 'luxury', name: 'Luxury', icon: 'bi bi-gem' }
        ];
    }
}

// Theme-specific animations and effects
class ThemeEffects {
    constructor(themeSwitcher) {
        this.themeSwitcher = themeSwitcher;
        this.init();
    }

    init() {
        window.addEventListener('themeChanged', (e) => {
            this.applyThemeEffects(e.detail.theme);
        });
    }

    applyThemeEffects(theme) {
        switch (theme) {
            case 'luxury':
                this.enableLuxuryEffects();
                break;
            case 'dark':
                this.enableDarkEffects();
                break;
            default:
                this.disableSpecialEffects();
                break;
        }
    }

    enableLuxuryEffects() {
        // Add luxury-specific animations and effects
        this.addGoldParticles();
        this.enableShimmerEffects();
    }

    enableDarkEffects() {
        // Add dark theme specific effects
        this.addSubtleGlow();
    }

    disableSpecialEffects() {
        // Remove any special effects
        this.removeParticles();
        this.removeGlowEffects();
    }

    addGoldParticles() {
        // Create subtle gold particle effect for luxury theme
        if (document.querySelector('.gold-particles')) return;

        const particles = document.createElement('div');
        particles.className = 'gold-particles';
        particles.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.1;
        `;

        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: absolute;
                width: 2px;
                height: 2px;
                background: #d4af37;
                border-radius: 50%;
                animation: float ${3 + Math.random() * 4}s ease-in-out infinite;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation-delay: ${Math.random() * 2}s;
            `;
            particles.appendChild(particle);
        }

        document.body.appendChild(particles);

        // Add floating animation
        if (!document.querySelector('#particle-animation')) {
            const style = document.createElement('style');
            style.id = 'particle-animation';
            style.textContent = `
                @keyframes float {
                    0%, 100% { transform: translateY(0px) rotate(0deg); }
                    50% { transform: translateY(-20px) rotate(180deg); }
                }
            `;
            document.head.appendChild(style);
        }
    }

    enableShimmerEffects() {
        // Add shimmer effect to luxury theme elements
        const style = document.createElement('style');
        style.id = 'shimmer-effects';
        style.textContent = `
            [data-bs-theme="luxury"] .property-card:hover,
            [data-bs-theme="luxury"] .testimonial-card:hover {
                animation: luxuryShimmer 2s ease-in-out;
            }
            
            @keyframes luxuryShimmer {
                0% { box-shadow: 0 8px 32px rgba(212, 175, 55, 0.1); }
                50% { box-shadow: 0 12px 40px rgba(212, 175, 55, 0.3); }
                100% { box-shadow: 0 8px 32px rgba(212, 175, 55, 0.1); }
            }
        `;
        document.head.appendChild(style);
    }

    addSubtleGlow() {
        // Add subtle glow effects for dark theme
        const style = document.createElement('style');
        style.id = 'dark-glow-effects';
        style.textContent = `
            [data-bs-theme="dark"] .btn-warning:hover {
                box-shadow: 0 0 20px rgba(255, 193, 7, 0.3);
            }
            
            [data-bs-theme="dark"] .property-card:hover {
                box-shadow: 0 8px 32px rgba(255, 193, 7, 0.1);
            }
        `;
        document.head.appendChild(style);
    }

    removeParticles() {
        const particles = document.querySelector('.gold-particles');
        if (particles) {
            particles.remove();
        }
        
        const animation = document.querySelector('#particle-animation');
        if (animation) {
            animation.remove();
        }
    }

    removeGlowEffects() {
        const shimmer = document.querySelector('#shimmer-effects');
        if (shimmer) {
            shimmer.remove();
        }
        
        const glow = document.querySelector('#dark-glow-effects');
        if (glow) {
            glow.remove();
        }
    }
}

// Initialize theme switcher when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const themeSwitcher = new ThemeSwitcher();
    const themeEffects = new ThemeEffects(themeSwitcher);

    // Make theme switcher globally available
    window.ThemeSwitcher = themeSwitcher;
    window.ThemeEffects = themeEffects;

    // Handle URL theme parameter if it exists
    const urlParams = new URLSearchParams(window.location.search);
    const themeParam = urlParams.get('theme');
    if (themeParam && ['light', 'dark', 'luxury'].includes(themeParam)) {
        themeSwitcher.setTheme(themeParam);
    }

    // Add keyboard shortcuts for theme switching
    document.addEventListener('keydown', function(e) {
        // Ctrl+Shift+T to cycle themes
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            const themes = ['light', 'dark', 'luxury'];
            const currentIndex = themes.indexOf(themeSwitcher.getCurrentTheme());
            const nextIndex = (currentIndex + 1) % themes.length;
            themeSwitcher.setTheme(themes[nextIndex]);
        }

        // Ctrl+Shift+L for light theme
        if (e.ctrlKey && e.shiftKey && e.key === 'L') {
            e.preventDefault();
            themeSwitcher.setTheme('light');
        }

        // Ctrl+Shift+D for dark theme
        if (e.ctrlKey && e.shiftKey && e.key === 'D') {
            e.preventDefault();
            themeSwitcher.setTheme('dark');
        }

        // Ctrl+Shift+X for luxury theme
        if (e.ctrlKey && e.shiftKey && e.key === 'X') {
            e.preventDefault();
            themeSwitcher.setTheme('luxury');
        }
    });
});


