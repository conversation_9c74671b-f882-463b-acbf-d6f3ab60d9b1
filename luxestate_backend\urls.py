"""
URL configuration for luxestate_backend project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView
from rest_framework.routers import DefaultRouter
from rest_framework.authtoken.views import obtain_auth_token

# API Router
router = DefaultRouter()

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),
    
    # API URLs
    path('api/', include(router.urls)),
    path('api/auth/', include('rest_framework.urls')),
    path('api/token/', obtain_auth_token, name='api_token_auth'),
    
    # App URLs
    path('api/properties/', include('properties.urls')),
    path('api/users/', include('users.urls')),
    path('api/messages/', include('messages.urls')),
    path('api/analytics/', include('analytics.urls')),
    path('api/payments/', include('payments.urls')),
    
    # Authentication
    path('accounts/', include('allauth.urls')),
    
    # Frontend (serve static files for development)
    path('', TemplateView.as_view(template_name='index.html'), name='home'),
    path('listings/', TemplateView.as_view(template_name='listings.html'), name='listings'),
    path('property-detail/', TemplateView.as_view(template_name='property-detail.html'), name='property_detail'),
    path('dashboard/', TemplateView.as_view(template_name='dashboard.html'), name='dashboard'),
    path('favorites/', TemplateView.as_view(template_name='favorites.html'), name='favorites'),
    path('add-property/', TemplateView.as_view(template_name='add-property.html'), name='add_property'),
    path('about/', TemplateView.as_view(template_name='about.html'), name='about'),
    path('contact/', TemplateView.as_view(template_name='contact.html'), name='contact'),
    path('blog/', TemplateView.as_view(template_name='blog.html'), name='blog'),
    path('blog-detail/', TemplateView.as_view(template_name='blog-detail.html'), name='blog_detail'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# Admin site customization
admin.site.site_header = "LuxEstate Administration"
admin.site.site_title = "LuxEstate Admin"
admin.site.index_title = "Welcome to LuxEstate Administration"
