"""
Views for the payments app.
"""

from rest_framework import viewsets, permissions, status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.conf import settings
from django.utils import timezone
import stripe

from .models import PaymentMethod, Transaction, Subscription, Invoice, Commission

stripe.api_key = settings.STRIPE_SECRET_KEY

class PaymentMethodViewSet(viewsets.ModelViewSet):
    """ViewSet for payment methods."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get payment methods for current user."""
        return PaymentMethod.objects.filter(user=self.request.user, is_active=True)

class TransactionViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for transactions."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get transactions for current user."""
        return Transaction.objects.filter(user=self.request.user)

class SubscriptionViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for subscriptions."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get subscriptions for current user."""
        return Subscription.objects.filter(user=self.request.user)

class InvoiceViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for invoices."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get invoices for current user."""
        return Invoice.objects.filter(user=self.request.user)

class CommissionViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for commissions."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get commissions for current user."""
        return Commission.objects.filter(agent=self.request.user)

class CreatePaymentIntentView(APIView):
    """Create Stripe payment intent."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """Create payment intent."""
        try:
            amount = request.data.get('amount')  # Amount in cents
            currency = request.data.get('currency', 'usd')
            
            intent = stripe.PaymentIntent.create(
                amount=amount,
                currency=currency,
                metadata={'user_id': request.user.id}
            )
            
            return Response({
                'client_secret': intent.client_secret,
                'payment_intent_id': intent.id
            })
        
        except stripe.error.StripeError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

class StripeWebhookView(APIView):
    """Handle Stripe webhooks."""
    
    permission_classes = []
    
    def post(self, request):
        """Handle webhook events."""
        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
        endpoint_secret = settings.STRIPE_WEBHOOK_SECRET
        
        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, endpoint_secret
            )
        except ValueError:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        except stripe.error.SignatureVerificationError:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        
        # Handle the event
        if event['type'] == 'payment_intent.succeeded':
            self.handle_payment_succeeded(event['data']['object'])
        elif event['type'] == 'payment_intent.payment_failed':
            self.handle_payment_failed(event['data']['object'])
        
        return Response(status=status.HTTP_200_OK)
    
    def handle_payment_succeeded(self, payment_intent):
        """Handle successful payment."""
        # Update transaction status
        try:
            transaction = Transaction.objects.get(
                stripe_payment_intent_id=payment_intent['id']
            )
            transaction.status = 'completed'
            transaction.processed_at = timezone.now()
            transaction.save()
        except Transaction.DoesNotExist:
            pass
    
    def handle_payment_failed(self, payment_intent):
        """Handle failed payment."""
        try:
            transaction = Transaction.objects.get(
                stripe_payment_intent_id=payment_intent['id']
            )
            transaction.status = 'failed'
            transaction.save()
        except Transaction.DoesNotExist:
            pass

class SubscriptionPlansView(APIView):
    """Get available subscription plans."""
    
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    
    def get(self, request):
        """Get subscription plans."""
        plans = [
            {
                'id': 'basic',
                'name': 'Basic',
                'price': 29.99,
                'features': [
                    'Up to 5 property listings',
                    'Basic analytics',
                    'Email support'
                ]
            },
            {
                'id': 'premium',
                'name': 'Premium',
                'price': 59.99,
                'features': [
                    'Up to 25 property listings',
                    'Advanced analytics',
                    'Priority support',
                    'Featured listings'
                ]
            },
            {
                'id': 'professional',
                'name': 'Professional',
                'price': 99.99,
                'features': [
                    'Unlimited property listings',
                    'Full analytics suite',
                    'Priority support',
                    'Featured listings',
                    'Custom branding'
                ]
            }
        ]
        
        return Response({'plans': plans})
