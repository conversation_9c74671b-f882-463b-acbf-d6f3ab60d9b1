# 🏠 LuxEstate Django Backend - Complete Installation Guide

A comprehensive luxury real estate platform backend built with Django REST Framework.

## ✨ Features Overview

### 🏘️ Property Management
- Complete CRUD operations for properties
- Advanced search & filtering with multiple criteria
- Property categories with hierarchical structure
- Image gallery with thumbnail generation
- Virtual tours and video integration
- Sale & rent listings with flexible pricing
- Featured & premium property promotions
- Property analytics and performance tracking

### 👥 User Management
- Multi-role system (Buyers, Sellers, Agents, Admins)
- User profiles with detailed information
- Authentication & authorization with JWT tokens
- Favorites system for properties
- Search history tracking
- Notification preferences

### 💬 Communication System
- Real-time messaging with WebSocket support
- Property inquiries with automated responses
- Conversation management between users
- Notification system for all activities
- Email integration for important updates

### 📊 Analytics & Reporting
- Property performance metrics
- User activity tracking
- Market trends analysis
- Search analytics for insights
- Custom reports generation
- Dashboard analytics for users

### 💳 Payment Integration
- Stripe integration for secure payments
- Subscription management for premium features
- Commission tracking for agents
- Invoice generation and management
- Multiple payment methods support

## 🚀 Installation Instructions

### Prerequisites
- Python 3.8 or higher
- Redis (for Celery and WebSocket support)
- PostgreSQL (optional, SQLite included for development)

### Option 1: Automated Installation (Recommended)

#### Linux/macOS:
```bash
chmod +x install.sh
./install.sh
```

#### Windows:
```cmd
install.bat
```

### Option 2: Manual Installation

#### Step 1: Create Virtual Environment
```bash
# Create virtual environment
python -m venv luxestate_env

# Activate virtual environment
# Linux/macOS:
source luxestate_env/bin/activate
# Windows:
luxestate_env\Scripts\activate.bat
```

#### Step 2: Install Dependencies
```bash
# Upgrade pip
pip install --upgrade pip

# Install requirements
pip install -r requirements.txt
```

#### Step 3: Environment Configuration
Create a `.env` file in the project root:
```env
# Django Settings
SECRET_KEY=django-insecure-your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (SQLite for development)
DB_ENGINE=django.db.backends.sqlite3
DB_NAME=db.sqlite3

# Email Settings (Console backend for development)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# Celery Settings
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Stripe Settings (Add your keys)
STRIPE_PUBLISHABLE_KEY=pk_test_your_key_here
STRIPE_SECRET_KEY=sk_test_your_key_here
```

#### Step 4: Database Setup
```bash
# Create and run migrations
python manage.py makemigrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic --noinput
```

#### Step 5: Start Development Server
```bash
python manage.py runserver
```

## 🌐 API Endpoints

### Authentication
- `POST /api/users/auth/register/` - User registration
- `POST /api/users/auth/login/` - User login
- `POST /api/users/auth/logout/` - User logout
- `GET /api/users/auth/me/` - Current user info

### Properties
- `GET /api/properties/properties/` - List properties
- `POST /api/properties/properties/` - Create property
- `GET /api/properties/properties/{id}/` - Property details
- `PUT /api/properties/properties/{id}/` - Update property
- `DELETE /api/properties/properties/{id}/` - Delete property
- `POST /api/properties/properties/search/` - Advanced search
- `GET /api/properties/properties/featured/` - Featured properties
- `POST /api/properties/properties/{id}/favorite/` - Toggle favorite

### Messages
- `GET /api/messages/conversations/` - List conversations
- `POST /api/messages/conversations/` - Create conversation
- `GET /api/messages/conversations/{id}/messages/` - Get messages
- `POST /api/messages/conversations/{id}/send_message/` - Send message
- `POST /api/messages/inquiries/` - Submit property inquiry

### Analytics
- `GET /api/analytics/dashboard/` - Dashboard analytics
- `POST /api/analytics/reports/` - Generate reports
- `GET /api/analytics/property-analytics/` - Property analytics
- `GET /api/analytics/market-trends/` - Market trends

### Payments
- `GET /api/payments/subscription-plans/` - Available plans
- `POST /api/payments/create-payment-intent/` - Create payment
- `GET /api/payments/transactions/` - Transaction history
- `GET /api/payments/subscriptions/` - User subscriptions

## 🏗️ Project Structure

```
luxestate_backend/
├── luxestate_backend/          # Main project settings
│   ├── settings.py            # Django settings
│   ├── urls.py               # Main URL configuration
│   ├── wsgi.py              # WSGI configuration
│   ├── asgi.py              # ASGI configuration (WebSocket)
│   └── celery.py            # Celery configuration
├── properties/               # Property management app
│   ├── models.py            # Property models
│   ├── views.py             # API views
│   ├── serializers.py       # DRF serializers
│   ├── filters.py           # Django filters
│   ├── permissions.py       # Custom permissions
│   └── urls.py              # Property URLs
├── users/                   # User management app
│   ├── models.py           # User models
│   ├── views.py            # User API views
│   ├── serializers.py      # User serializers
│   └── urls.py             # User URLs
├── messages/               # Communication app
│   ├── models.py          # Message models
│   ├── views.py           # Message API views
│   ├── serializers.py     # Message serializers
│   ├── consumers.py       # WebSocket consumers
│   ├── routing.py         # WebSocket routing
│   └── urls.py            # Message URLs
├── analytics/             # Analytics app
│   ├── models.py         # Analytics models
│   ├── views.py          # Analytics views
│   └── urls.py           # Analytics URLs
├── payments/             # Payment processing app
│   ├── models.py        # Payment models
│   ├── views.py         # Payment views
│   └── urls.py          # Payment URLs
├── static/              # Static files (CSS, JS, Images)
├── media/               # User uploaded files
├── templates/           # HTML templates
├── requirements.txt     # Python dependencies
├── manage.py           # Django management script
├── install.sh          # Linux/macOS installation script
├── install.bat         # Windows installation script
└── README.md           # This file
```

## 🔧 Optional Services

### Redis Setup
Redis is required for Celery (background tasks) and WebSocket support.

#### Installation:
- **Ubuntu/Debian:** `sudo apt-get install redis-server`
- **macOS:** `brew install redis`
- **Windows:** Download from https://redis.io/download

#### Start Redis:
```bash
redis-server
```

### Celery Workers
For background task processing:

```bash
# Start Celery worker
celery -A luxestate_backend worker -l info

# Start Celery beat (scheduler)
celery -A luxestate_backend beat -l info
```

## 🧪 Testing

Run the test suite:
```bash
python manage.py test
```

## 🚀 Production Deployment

### Environment Variables for Production
```env
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
SECRET_KEY=your-production-secret-key

# PostgreSQL Database
DB_ENGINE=django.db.backends.postgresql
DB_NAME=luxestate_production
DB_USER=your_db_user
DB_PASSWORD=your_secure_password
DB_HOST=your_db_host
DB_PORT=5432

# Email Settings
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# Stripe Production Keys
STRIPE_PUBLISHABLE_KEY=pk_live_your_live_key
STRIPE_SECRET_KEY=sk_live_your_live_key
```

### Using Gunicorn
```bash
pip install gunicorn
gunicorn luxestate_backend.wsgi:application --bind 0.0.0.0:8000
```

## 📚 API Documentation

Once the server is running, visit:
- **Admin Panel:** `http://localhost:8000/admin/`
- **API Root:** `http://localhost:8000/api/`
- **Frontend:** `http://localhost:8000/`

## 🆘 Troubleshooting

### Common Issues:

1. **ModuleNotFoundError**: Make sure virtual environment is activated
2. **Database errors**: Run `python manage.py migrate`
3. **Static files not loading**: Run `python manage.py collectstatic`
4. **Redis connection error**: Make sure Redis server is running

### Getting Help:
- Check Django logs in `logs/django.log`
- Use `python manage.py shell` for debugging
- Run `python manage.py check` for system checks

## 🎉 Success!

If everything is set up correctly, you should see:
- ✅ Django development server running on http://127.0.0.1:8000/
- ✅ Admin panel accessible at http://127.0.0.1:8000/admin/
- ✅ API endpoints responding at http://127.0.0.1:8000/api/
- ✅ Frontend pages loading with backend integration

**Happy coding! 🚀**
