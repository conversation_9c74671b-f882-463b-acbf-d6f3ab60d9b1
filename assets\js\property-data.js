// ===== PROPERTY DATA AND DYNAMIC CONTENT =====

// Sample property data
const sampleProperties = [
    {
        id: 1,
        title: "Luxury Penthouse Manhattan",
        price: 8500000,
        listingType: "sale", // "sale" or "rent"
        rentPrice: null, // monthly rent if applicable
        location: "Upper East Side, New York",
        type: "Penthouse",
        bedrooms: 4,
        bathrooms: 3,
        area: 3200,
        image: "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        images: [
            "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
        ],
        featured: true,
        badge: "Featured",
        description: "Stunning penthouse with panoramic city views, modern amenities, and luxury finishes throughout.",
        features: ["City Views", "Modern Kitchen", "Balcony", "Gym", "Concierge"]
    },
    {
        id: 2,
        title: "Modern Villa Beverly Hills",
        price: 12000000,
        listingType: "sale",
        rentPrice: null,
        location: "Beverly Hills, California",
        type: "Villa",
        bedrooms: 6,
        bathrooms: 5,
        area: 5500,
        image: "https://images.unsplash.com/photo-1600607687644-aac4c3eac7f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        images: [
            "https://images.unsplash.com/photo-1600607687644-aac4c3eac7f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
        ],
        featured: true,
        badge: "New",
        description: "Contemporary villa with infinity pool, wine cellar, and state-of-the-art smart home technology.",
        features: ["Pool", "Wine Cellar", "Smart Home", "Garden", "Garage"]
    },
    {
        id: 3,
        title: "Waterfront Mansion Miami",
        price: 15500000,
        listingType: "sale",
        rentPrice: null,
        location: "Miami Beach, Florida",
        type: "Mansion",
        bedrooms: 8,
        bathrooms: 7,
        area: 8200,
        image: "https://images.unsplash.com/photo-1600607688960-e095ff8d5e6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        images: [
            "https://images.unsplash.com/photo-1600607688960-e095ff8d5e6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
        ],
        featured: true,
        badge: "Exclusive",
        description: "Magnificent waterfront estate with private beach access, yacht dock, and resort-style amenities.",
        features: ["Beach Access", "Yacht Dock", "Pool", "Tennis Court", "Guest House"]
    },
    {
        id: 4,
        title: "Historic Brownstone Boston",
        price: 4200000,
        listingType: "sale",
        rentPrice: null,
        location: "Back Bay, Boston",
        type: "Townhouse",
        bedrooms: 5,
        bathrooms: 4,
        area: 4100,
        image: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        images: [
            "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1600585154526-990dced4db0d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1600585154084-4e5fe7c39198?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
        ],
        featured: false,
        badge: "Historic",
        description: "Beautifully restored Victorian brownstone with original architectural details and modern updates.",
        features: ["Historic Details", "Fireplace", "Garden", "Parking", "Renovated"]
    },
    {
        id: 5,
        title: "Mountain Retreat Aspen",
        price: 9800000,
        listingType: "rent",
        rentPrice: 25000, // monthly rent
        location: "Aspen, Colorado",
        type: "Chalet",
        bedrooms: 7,
        bathrooms: 6,
        area: 6800,
        image: "https://images.unsplash.com/photo-1600566753151-384129cf4e3e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        images: [
            "https://images.unsplash.com/photo-1600566753151-384129cf4e3e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1600566752355-35792bedcfea?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
        ],
        featured: true,
        badge: "Mountain View",
        description: "Luxury ski chalet with breathtaking mountain views, hot tub, and ski-in/ski-out access.",
        features: ["Mountain Views", "Ski Access", "Hot Tub", "Fireplace", "Wine Cellar"]
    },
    {
        id: 6,
        title: "Downtown Loft Chicago",
        price: 2800000,
        listingType: "rent",
        rentPrice: 8500, // monthly rent
        location: "River North, Chicago",
        type: "Loft",
        bedrooms: 3,
        bathrooms: 2,
        area: 2400,
        image: "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        images: [
            "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
        ],
        featured: false,
        badge: "Urban",
        description: "Industrial loft with exposed brick, high ceilings, and stunning city skyline views.",
        features: ["City Views", "Exposed Brick", "High Ceilings", "Rooftop Access", "Modern Kitchen"]
    }
];

// Sample testimonials data
const sampleTestimonials = [
    {
        id: 1,
        name: "Sarah Johnson",
        role: "Property Investor",
        avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
        rating: 5,
        text: "LuxEstate helped me find the perfect investment property. Their expertise and attention to detail made the entire process seamless."
    },
    {
        id: 2,
        name: "Michael Chen",
        role: "Tech Executive",
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
        rating: 5,
        text: "Outstanding service! The team at LuxEstate understood exactly what I was looking for and delivered beyond my expectations."
    },
    {
        id: 3,
        name: "Emily Rodriguez",
        role: "Interior Designer",
        avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
        rating: 5,
        text: "Professional, knowledgeable, and truly caring. LuxEstate made buying my dream home an absolute pleasure."
    },
    {
        id: 4,
        name: "David Thompson",
        role: "Business Owner",
        avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
        rating: 5,
        text: "The luxury properties showcased by LuxEstate are exceptional. Their market knowledge is unparalleled."
    }
];

// Load featured properties
function loadFeaturedProperties() {
    const container = document.querySelector('.featured-properties .swiper-wrapper');
    if (!container) return;

    const featuredProperties = sampleProperties.filter(property => property.featured);
    
    container.innerHTML = featuredProperties.map(property => createPropertyCard(property)).join('');
}

// Load testimonials
function loadTestimonials() {
    const container = document.querySelector('.testimonials-swiper .swiper-wrapper');
    if (!container) return;

    container.innerHTML = sampleTestimonials.map(testimonial => createTestimonialCard(testimonial)).join('');
}

// Create property card HTML
function createPropertyCard(property) {
    const favorites = getFavorites();
    const isFavorite = favorites.includes(property.id.toString());

    // Determine price display based on listing type
    let priceDisplay = '';
    if (property.listingType === 'rent') {
        priceDisplay = `${formatPrice(property.rentPrice)}/month`;
        if (property.price) {
            priceDisplay += ` <small class="text-muted">(${formatPrice(property.price)} to buy)</small>`;
        }
    } else {
        priceDisplay = formatPrice(property.price);
    }

    // Determine listing type badge
    const listingBadge = property.listingType === 'rent' ? 'For Rent' : 'For Sale';

    return `
        <div class="swiper-slide">
            <div class="card property-card h-100">
                <div class="property-image">
                    <img src="${property.image}" alt="${property.title}" class="card-img-top">
                    <div class="property-badge">${property.badge}</div>
                    <div class="listing-type-badge ${property.listingType}">${listingBadge}</div>
                    <button class="favorite-btn ${isFavorite ? 'active' : ''}" data-property-id="${property.id}">
                        <i class="bi bi-heart${isFavorite ? '-fill' : ''}"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="property-price">${priceDisplay}</div>
                    <h5 class="card-title">${property.title}</h5>
                    <p class="property-location">
                        <i class="bi bi-geo-alt me-1"></i>${property.location}
                    </p>
                    <div class="property-features">
                        <span class="property-feature">
                            <i class="bi bi-house me-1"></i>${property.bedrooms} Beds
                        </span>
                        <span class="property-feature">
                            <i class="bi bi-droplet me-1"></i>${property.bathrooms} Baths
                        </span>
                        <span class="property-feature">
                            <i class="bi bi-arrows-angle-expand me-1"></i>${formatNumber(property.area)} sqft
                        </span>
                    </div>
                    <div class="d-flex gap-2 mt-3">
                        <a href="property-detail.html?id=${property.id}" class="btn btn-warning flex-fill">View Details</a>
                        <button class="btn btn-outline-warning" onclick="scheduleViewing(${property.id})">
                            <i class="bi bi-calendar"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Create testimonial card HTML
function createTestimonialCard(testimonial) {
    const stars = '★'.repeat(testimonial.rating) + '☆'.repeat(5 - testimonial.rating);
    
    return `
        <div class="swiper-slide">
            <div class="card testimonial-card h-100">
                <div class="card-body text-center">
                    <img src="${testimonial.avatar}" alt="${testimonial.name}" class="testimonial-avatar">
                    <div class="testimonial-rating text-warning mb-3">
                        ${stars}
                    </div>
                    <p class="testimonial-text">"${testimonial.text}"</p>
                    <h6 class="testimonial-author">${testimonial.name}</h6>
                    <small class="testimonial-role">${testimonial.role}</small>
                </div>
            </div>
        </div>
    `;
}

// Load all properties for listings page
function loadAllProperties(filters = {}, sortBy = 'price-desc') {
    let properties = [...sampleProperties];
    
    // Apply filters
    if (filters.location) {
        properties = properties.filter(p => 
            p.location.toLowerCase().includes(filters.location.toLowerCase())
        );
    }
    
    if (filters.type && filters.type !== 'any') {
        properties = properties.filter(p => 
            p.type.toLowerCase() === filters.type.toLowerCase()
        );
    }
    
    if (filters.bedrooms) {
        properties = properties.filter(p => p.bedrooms >= parseInt(filters.bedrooms));
    }
    
    if (filters.minPrice) {
        properties = properties.filter(p => p.price >= parseInt(filters.minPrice));
    }
    
    if (filters.maxPrice) {
        properties = properties.filter(p => p.price <= parseInt(filters.maxPrice));
    }
    
    // Apply sorting
    switch (sortBy) {
        case 'price-asc':
            properties.sort((a, b) => a.price - b.price);
            break;
        case 'price-desc':
            properties.sort((a, b) => b.price - a.price);
            break;
        case 'bedrooms-desc':
            properties.sort((a, b) => b.bedrooms - a.bedrooms);
            break;
        case 'area-desc':
            properties.sort((a, b) => b.area - a.area);
            break;
        case 'newest':
            properties.sort((a, b) => b.id - a.id);
            break;
        default:
            break;
    }
    
    return properties;
}

// Get property by ID
function getPropertyById(id) {
    return sampleProperties.find(property => property.id === parseInt(id));
}

// Schedule viewing function
function scheduleViewing(propertyId) {
    const property = getPropertyById(propertyId);
    if (property && window.LuxEstate && window.LuxEstate.showToast) {
        window.LuxEstate.showToast(
            'Viewing Scheduled',
            `We'll contact you soon to schedule a viewing for ${property.title}`,
            'success'
        );
    }
}

// Search properties
function searchProperties(query) {
    const searchTerm = query.toLowerCase();
    return sampleProperties.filter(property => 
        property.title.toLowerCase().includes(searchTerm) ||
        property.location.toLowerCase().includes(searchTerm) ||
        property.type.toLowerCase().includes(searchTerm) ||
        property.description.toLowerCase().includes(searchTerm)
    );
}

// Get similar properties
function getSimilarProperties(propertyId, limit = 3) {
    const property = getPropertyById(propertyId);
    if (!property) return [];

    return sampleProperties
        .filter(p => p.id !== propertyId && p.type === property.type)
        .slice(0, limit);
}

// Utility functions
function formatPrice(price) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(price);
}

function formatNumber(num) {
    return new Intl.NumberFormat('en-US').format(num);
}

// Export functions for global use
window.PropertyData = {
    sampleProperties,
    sampleTestimonials,
    loadFeaturedProperties,
    loadTestimonials,
    loadAllProperties,
    getPropertyById,
    scheduleViewing,
    searchProperties,
    getSimilarProperties,
    createPropertyCard,
    createTestimonialCard,
    formatPrice,
    formatNumber
};

// Make scheduleViewing globally available for HTML onclick handlers
window.scheduleViewing = scheduleViewing;
