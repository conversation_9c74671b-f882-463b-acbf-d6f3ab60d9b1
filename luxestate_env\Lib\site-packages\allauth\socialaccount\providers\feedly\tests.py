from allauth.socialaccount.tests import OAuth2TestsMixin
from allauth.tests import MockedResponse, TestCase

from .provider import FeedlyProvider


class FeedlyTests(OAuth2TestsMixin, TestCase):
    provider_id = FeedlyProvider.id

    def get_mocked_response(self):
        return MockedResponse(
            200,
            """
{
  "id": "c805fcbf-3acf-4302-a97e-d82f9d7c897f",
  "email": "<EMAIL>",
  "givenName": "<PERSON>",
  "familyName": "<PERSON>",
  "picture": "https://www.google.com/profile_images/**********/bigger.jpg",
  "gender": "male",
  "locale": "en",
  "reader": "****************",
  "google": "115562565652656565656",
  "twitter": "jimsmith",
  "facebook": "",
  "wave": "2013.7"
}""",
        )
