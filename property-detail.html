<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Property Details - LuxEstate</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css">
    
    <!-- AOS CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <style>
        /* Property Detail Specific Styles */
        .property-detail-swiper {
            height: 500px;
            border-radius: var(--border-radius);
            overflow: hidden;
        }
        
        .property-detail-swiper img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .property-thumbs-swiper {
            height: 100px;
            margin-top: 10px;
        }
        
        .property-thumbs-swiper .swiper-slide {
            opacity: 0.5;
            cursor: pointer;
            border-radius: var(--border-radius);
            overflow: hidden;
            transition: var(--transition);
        }
        
        .property-thumbs-swiper .swiper-slide-thumb-active {
            opacity: 1;
            border: 2px solid var(--luxury-gold);
        }
        
        .property-thumbs-swiper img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .property-features-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .property-feature-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .property-feature-icon {
            width: 40px;
            height: 40px;
            background: var(--bs-secondary-bg);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--luxury-gold);
        }
        
        .map-container {
            height: 400px;
            border-radius: var(--border-radius);
            overflow: hidden;
        }
        
        .agent-card {
            border-radius: var(--border-radius);
            overflow: hidden;
            border: 1px solid var(--bs-border-color);
            background: var(--bs-secondary-bg);
        }
        
        .agent-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--luxury-gold);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top luxury-nav">
        <div class="container">
            <a class="navbar-brand fw-bold fs-3" href="index.html">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </a>
            
            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Desktop Navigation -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="listings.html">Properties</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.html">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">Contact</a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <!-- Theme Switcher -->
                    <div class="dropdown me-3">
                        <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-palette"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item theme-option" href="#" data-theme="light">
                                <i class="bi bi-sun"></i> Light
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="dark">
                                <i class="bi bi-moon"></i> Dark
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="luxury">
                                <i class="bi bi-gem"></i> Luxury
                            </a></li>
                        </ul>
                    </div>
                    
                    <a href="favorites.html" class="btn btn-outline-light btn-sm me-2">
                        <i class="bi bi-heart"></i> <span class="d-none d-md-inline">Favorites</span>
                    </a>
                    <a href="login.html" class="btn btn-warning btn-sm me-2">Login</a>
                    <a href="add-property.html" class="btn btn-outline-warning btn-sm">
                        <i class="bi bi-plus-circle"></i> <span class="d-none d-md-inline">Add Property</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Navigation Offcanvas -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="mobileNav">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="index.html">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="listings.html">Properties</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="about.html">About</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="blog.html">Blog</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="contact.html">Contact</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="favorites.html">Favorites</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.html">Dashboard</a>
                </li>
            </ul>
            <hr>
            <div class="d-grid gap-2">
                <a href="login.html" class="btn btn-warning">Login</a>
                <a href="add-property.html" class="btn btn-outline-warning">Add Property</a>
            </div>
        </div>
    </div>

    <!-- Property Detail Content -->
    <div class="container mt-5 pt-5">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mt-3">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.html">Home</a></li>
                <li class="breadcrumb-item"><a href="listings.html">Properties</a></li>
                <li class="breadcrumb-item active" aria-current="page" id="propertyTitle">Property Detail</li>
            </ol>
        </nav>
        
        <!-- Property Header -->
        <div class="row align-items-center mb-4">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-2" id="propertyDetailTitle">Property Title</h1>
                <p class="lead mb-0" id="propertyDetailLocation">
                    <i class="bi bi-geo-alt text-warning me-2"></i>Location
                </p>
            </div>
            <div class="col-lg-4 text-lg-end mt-3 mt-lg-0">
                <h2 class="display-6 fw-bold text-warning mb-3" id="propertyDetailPrice">$0</h2>
                <div class="d-flex gap-2 justify-content-lg-end">
                    <button class="btn btn-outline-warning" id="favoriteBtn">
                        <i class="bi bi-heart"></i> Add to Favorites
                    </button>
                    <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#scheduleModal">
                        <i class="bi bi-calendar me-2"></i>Schedule Viewing
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Property Images -->
    <div class="container mb-5">
        <div class="row">
            <div class="col-12">
                <!-- Main Swiper -->
                <div class="swiper property-detail-swiper">
                    <div class="swiper-wrapper" id="propertyDetailImages">
                        <!-- Images will be loaded dynamically -->
                    </div>
                    <div class="swiper-pagination"></div>
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                </div>
                
                <!-- Thumbs Swiper -->
                <div class="swiper property-thumbs-swiper">
                    <div class="swiper-wrapper" id="propertyDetailThumbs">
                        <!-- Thumbnails will be loaded dynamically -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Property Details -->
    <div class="container mb-5">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Overview -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h4 class="fw-bold mb-4">Property Overview</h4>
                        <div class="row text-center g-3 mb-4">
                            <div class="col-6 col-md-3">
                                <div class="p-3 rounded bg-light">
                                    <i class="bi bi-house fs-4 text-warning mb-2"></i>
                                    <h5 class="fw-bold mb-0" id="propertyDetailBedrooms">0</h5>
                                    <p class="text-muted mb-0">Bedrooms</p>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="p-3 rounded bg-light">
                                    <i class="bi bi-droplet fs-4 text-warning mb-2"></i>
                                    <h5 class="fw-bold mb-0" id="propertyDetailBathrooms">0</h5>
                                    <p class="text-muted mb-0">Bathrooms</p>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="p-3 rounded bg-light">
                                    <i class="bi bi-arrows-angle-expand fs-4 text-warning mb-2"></i>
                                    <h5 class="fw-bold mb-0" id="propertyDetailArea">0</h5>
                                    <p class="text-muted mb-0">Square Feet</p>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="p-3 rounded bg-light">
                                    <i class="bi bi-house-door fs-4 text-warning mb-2"></i>
                                    <h5 class="fw-bold mb-0" id="propertyDetailType">Type</h5>
                                    <p class="text-muted mb-0">Property Type</p>
                                </div>
                            </div>
                        </div>
                        
                        <h5 class="fw-bold mb-3">Description</h5>
                        <p class="mb-4" id="propertyDetailDescription">Property description will be loaded here.</p>
                        
                        <h5 class="fw-bold mb-3">Features</h5>
                        <div class="property-features-list mb-0" id="propertyDetailFeatures">
                            <!-- Features will be loaded dynamically -->
                        </div>
                    </div>
                </div>
                
                <!-- Location -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h4 class="fw-bold mb-4">Location</h4>
                        <div class="map-container" id="propertyMap"></div>
                    </div>
                </div>
                
                <!-- Similar Properties -->
                <div class="card">
                    <div class="card-body">
                        <h4 class="fw-bold mb-4">Similar Properties</h4>
                        <div class="row g-4" id="similarProperties">
                            <!-- Similar properties will be loaded dynamically -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Agent Card -->
                <div class="card agent-card mb-4">
                    <div class="card-body text-center p-4">
                        <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80" alt="Agent" class="agent-image mb-3">
                        <h5 class="fw-bold mb-1">James Wilson</h5>
                        <p class="text-muted mb-3">Luxury Property Specialist</p>
                        <div class="d-flex justify-content-center gap-2 mb-3">
                            <a href="#" class="btn btn-sm btn-outline-warning rounded-circle">
                                <i class="bi bi-telephone"></i>
                            </a>
                            <a href="#" class="btn btn-sm btn-outline-warning rounded-circle">
                                <i class="bi bi-envelope"></i>
                            </a>
                            <a href="#" class="btn btn-sm btn-outline-warning rounded-circle">
                                <i class="bi bi-whatsapp"></i>
                            </a>
                        </div>
                        <button class="btn btn-warning w-100">Contact Agent</button>
                    </div>
                </div>
                
                <!-- Contact Form -->
                <div class="card">
                    <div class="card-body p-4">
                        <h5 class="fw-bold mb-3">Interested in this property?</h5>
                        <form class="needs-validation" novalidate data-form-type="property-inquiry">
                            <div class="mb-3">
                                <label for="name" class="form-label">Your Name</label>
                                <input type="text" class="form-control" id="name" required>
                                <div class="invalid-feedback">Please enter your name.</div>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" required>
                                <div class="invalid-feedback">Please enter a valid email.</div>
                            </div>
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" required>
                                <div class="invalid-feedback">Please enter your phone number.</div>
                            </div>
                            <div class="mb-3">
                                <label for="message" class="form-label">Message</label>
                                <textarea class="form-control" id="message" rows="4" required></textarea>
                                <div class="invalid-feedback">Please enter your message.</div>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    I agree to the <a href="#">terms and conditions</a>
                                </label>
                                <div class="invalid-feedback">You must agree before submitting.</div>
                            </div>
                            <button type="submit" class="btn btn-warning w-100">Send Inquiry</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Schedule Viewing Modal -->
    <div class="modal fade" id="scheduleModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Schedule a Viewing</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form class="needs-validation" novalidate data-form-type="schedule-viewing">
                        <div class="mb-3">
                            <label for="scheduleName" class="form-label">Your Name</label>
                            <input type="text" class="form-control" id="scheduleName" required>
                            <div class="invalid-feedback">Please enter your name.</div>
                        </div>
                        <div class="mb-3">
                            <label for="scheduleEmail" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="scheduleEmail" required>
                            <div class="invalid-feedback">Please enter a valid email.</div>
                        </div>
                        <div class="mb-3">
                            <label for="schedulePhone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="schedulePhone" required>
                            <div class="invalid-feedback">Please enter your phone number.</div>
                        </div>
                        <div class="mb-3">
                            <label for="scheduleDate" class="form-label">Preferred Date</label>
                            <input type="date" class="form-control" id="scheduleDate" required>
                            <div class="invalid-feedback">Please select a date.</div>
                        </div>
                        <div class="mb-3">
                            <label for="scheduleTime" class="form-label">Preferred Time</label>
                            <select class="form-select" id="scheduleTime" required>
                                <option value="">Select a time</option>
                                <option value="morning">Morning (9AM - 12PM)</option>
                                <option value="afternoon">Afternoon (12PM - 4PM)</option>
                                <option value="evening">Evening (4PM - 7PM)</option>
                            </select>
                            <div class="invalid-feedback">Please select a time.</div>
                        </div>
                        <div class="mb-3">
                            <label for="scheduleNotes" class="form-label">Additional Notes</label>
                            <textarea class="form-control" id="scheduleNotes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-warning" onclick="submitScheduleForm()">Schedule Viewing</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="bi bi-building text-warning me-2"></i>LuxEstate
                    </h5>
                    <p class="text-muted">Your premier destination for luxury real estate worldwide. We connect discerning clients with exceptional properties.</p>
                    <div class="social-links">
                        <a href="#" class="text-warning me-3"><i class="bi bi-facebook fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-twitter fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-instagram fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-linkedin fs-4"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.html" class="text-muted text-decoration-none">Home</a></li>
                        <li><a href="listings.html" class="text-muted text-decoration-none">Properties</a></li>
                        <li><a href="about.html" class="text-muted text-decoration-none">About</a></li>
                        <li><a href="contact.html" class="text-muted text-decoration-none">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Services</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted text-decoration-none">Buy Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Sell Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Rent Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Property Management</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Contact Info</h6>
                    <div class="contact-info">
                        <p class="text-muted mb-2">
                            <i class="bi bi-geo-alt text-warning me-2"></i>
                            123 Luxury Avenue, Premium District, NY 10001
                        </p>
                        <p class="text-muted mb-2">
                            <i class="bi bi-telephone text-warning me-2"></i>
                            +****************
                        </p>
                        <p class="text-muted mb-2">
                            <i class="bi bi-envelope text-warning me-2"></i>
                            <EMAIL>
                        </p>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 LuxEstate. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-muted text-decoration-none me-3">Privacy Policy</a>
                    <a href="#" class="text-muted text-decoration-none">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating Chat Widget -->
    <div class="floating-chat" id="floatingChat">
        <div class="chat-toggle" onclick="toggleChat()">
            <i class="bi bi-chat-dots"></i>
        </div>
        <div class="chat-window" id="chatWindow">
            <div class="chat-header">
                <h6 class="mb-0">Need Help?</h6>
                <button class="btn-close btn-close-white" onclick="toggleChat()"></button>
            </div>
            <div class="chat-body">
                <p class="mb-2">Hi! How can we help you today?</p>
                <div class="quick-actions">
                    <button class="btn btn-sm btn-outline-light mb-2 w-100">Schedule a Viewing</button>
                    <button class="btn btn-sm btn-outline-light mb-2 w-100">Get Property Info</button>
                    <button class="btn btn-sm btn-outline-light w-100">Speak to Agent</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
    
    <!-- AOS JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/theme-switcher.js"></script>
    <script src="assets/js/property-data.js"></script>
    <script src="assets/js/property-detail.js"></script>
</body>
</html>
