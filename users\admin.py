"""
Admin configuration for users app.
"""

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import User, UserProfile, Favorite, SearchHistory

class UserProfileInline(admin.StackedInline):
    """Inline admin for UserProfile."""
    
    model = UserProfile
    can_delete = False
    verbose_name_plural = 'Profile'
    
    fieldsets = (
        ('Agent Information', {
            'fields': (
                'license_number', 'agency_name', 'years_experience',
                'specializations'
            )
        }),
        ('Social Media', {
            'fields': (
                'facebook_url', 'twitter_url', 'linkedin_url', 'instagram_url'
            ),
            'classes': ('collapse',)
        }),
        ('Statistics', {
            'fields': (
                'properties_sold', 'total_sales_value', 'average_rating',
                'total_reviews'
            ),
            'classes': ('collapse',)
        }),
        ('Verification', {
            'fields': ('is_verified', 'verification_date'),
            'classes': ('collapse',)
        })
    )

@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Admin for User model."""
    
    list_display = [
        'username', 'email', 'first_name', 'last_name',
        'user_type', 'is_active', 'is_staff', 'date_joined'
    ]
    list_filter = [
        'user_type', 'is_active', 'is_staff', 'is_superuser',
        'date_joined', 'last_login'
    ]
    search_fields = ['username', 'email', 'first_name', 'last_name']
    ordering = ['-date_joined']
    
    fieldsets = BaseUserAdmin.fieldsets + (
        ('Profile Information', {
            'fields': (
                'user_type', 'phone_number', 'avatar', 'bio',
                'location', 'website'
            )
        }),
        ('Preferences', {
            'fields': (
                'email_notifications', 'sms_notifications',
                'push_notifications', 'marketing_emails'
            ),
            'classes': ('collapse',)
        }),
        ('Activity', {
            'fields': ('last_active',),
            'classes': ('collapse',)
        })
    )
    
    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('Profile Information', {
            'fields': (
                'email', 'first_name', 'last_name', 'user_type',
                'phone_number'
            )
        }),
    )
    
    inlines = [UserProfileInline]
    
    def avatar_preview(self, obj):
        """Show avatar preview."""
        if obj.avatar:
            return format_html('<img src="{}" width="50" height="50" style="border-radius: 50%;" />', obj.avatar.url)
        return "No avatar"
    avatar_preview.short_description = "Avatar"

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """Admin for UserProfile model."""
    
    list_display = [
        'user', 'agency_name', 'years_experience', 'properties_sold',
        'average_rating', 'is_verified'
    ]
    list_filter = ['is_verified', 'years_experience']
    search_fields = [
        'user__username', 'user__email', 'agency_name',
        'license_number', 'specializations'
    ]
    readonly_fields = [
        'properties_sold', 'total_sales_value', 'average_rating',
        'total_reviews', 'created_at', 'updated_at'
    ]
    
    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Agent Information', {
            'fields': (
                'license_number', 'agency_name', 'years_experience',
                'specializations'
            )
        }),
        ('Social Media', {
            'fields': (
                'facebook_url', 'twitter_url', 'linkedin_url', 'instagram_url'
            ),
            'classes': ('collapse',)
        }),
        ('Statistics', {
            'fields': (
                'properties_sold', 'total_sales_value', 'average_rating',
                'total_reviews'
            ),
            'classes': ('collapse',)
        }),
        ('Verification', {
            'fields': ('is_verified', 'verification_date')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('user')

@admin.register(Favorite)
class FavoriteAdmin(admin.ModelAdmin):
    """Admin for Favorite model."""
    
    list_display = ['user', 'property', 'created_at']
    list_filter = ['created_at']
    search_fields = ['user__username', 'property__title']
    readonly_fields = ['created_at']
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('user', 'property')

@admin.register(SearchHistory)
class SearchHistoryAdmin(admin.ModelAdmin):
    """Admin for SearchHistory model."""
    
    list_display = [
        'user_display', 'search_query', 'results_count',
        'ip_address', 'created_at'
    ]
    list_filter = ['created_at', 'results_count']
    search_fields = ['search_query', 'user__username', 'ip_address']
    readonly_fields = [
        'user', 'search_query', 'filters_applied', 'results_count',
        'ip_address', 'user_agent', 'created_at'
    ]
    
    def user_display(self, obj):
        """Display user or Anonymous."""
        return obj.user.username if obj.user else 'Anonymous'
    user_display.short_description = 'User'
    
    def has_add_permission(self, request):
        """Disable add permission."""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Disable change permission."""
        return False
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('user')
