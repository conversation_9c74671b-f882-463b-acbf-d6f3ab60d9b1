// ===== LUXURY REAL ESTATE WEBSITE MAIN JAVASCRIPT =====

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeAOS();
    initializeSwiper();
    initializeFloatingChat();
    initializeNavigation();
    initializeForms();
    loadFeaturedProperties();
    loadTestimonials();
    initializePropertyFilters();
    initializeFavorites();
});

// Initialize AOS (Animate On Scroll)
function initializeAOS() {
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 1000,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });
    }
}

// Initialize Swiper Carousels
function initializeSwiper() {
    if (typeof Swiper !== 'undefined') {
        // Featured Properties Swiper
        const featuredSwiper = new Swiper('.featured-properties', {
            slidesPerView: 1,
            spaceBetween: 30,
            loop: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            breakpoints: {
                640: {
                    slidesPerView: 1,
                },
                768: {
                    slidesPerView: 2,
                },
                1024: {
                    slidesPerView: 3,
                },
            },
        });

        // Testimonials Swiper
        const testimonialsSwiper = new Swiper('.testimonials-swiper', {
            slidesPerView: 1,
            spaceBetween: 30,
            loop: true,
            autoplay: {
                delay: 6000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            breakpoints: {
                768: {
                    slidesPerView: 2,
                },
                1024: {
                    slidesPerView: 3,
                },
            },
        });

        // Property Detail Image Swiper
        const propertyDetailSwiper = new Swiper('.property-detail-swiper', {
            slidesPerView: 1,
            spaceBetween: 0,
            loop: true,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            thumbs: {
                swiper: {
                    el: '.property-thumbs-swiper',
                    slidesPerView: 4,
                    spaceBetween: 10,
                    watchSlidesProgress: true,
                },
            },
        });
    }
}

// Floating Chat Widget
function initializeFloatingChat() {
    const chatToggle = document.querySelector('.chat-toggle');
    const chatWindow = document.querySelector('.chat-window');
    
    if (chatToggle && chatWindow) {
        chatToggle.addEventListener('click', toggleChat);
        
        // Close chat when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.floating-chat') && chatWindow.classList.contains('show')) {
                toggleChat();
            }
        });
    }
}

function toggleChat() {
    const chatWindow = document.querySelector('.chat-window');
    const chatToggle = document.querySelector('.chat-toggle');
    
    if (chatWindow && chatToggle) {
        chatWindow.classList.toggle('show');
        
        // Update icon
        const icon = chatToggle.querySelector('i');
        if (chatWindow.classList.contains('show')) {
            icon.className = 'bi bi-x-lg';
        } else {
            icon.className = 'bi bi-chat-dots';
        }
    }
}

// Navigation Enhancement
function initializeNavigation() {
    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        }
    });

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Active nav link highlighting
    const navLinks = document.querySelectorAll('.nav-link');
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPage) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

// Form Initialization and Validation
function initializeForms() {
    // Bootstrap form validation
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            } else {
                event.preventDefault();
                handleFormSubmission(form);
            }
            form.classList.add('was-validated');
        });
    });

    // Newsletter form
    const newsletterForm = document.querySelector('form[action*="newsletter"]');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleNewsletterSubmission(this);
        });
    }

    // Search form
    const searchForm = document.querySelector('.search-container form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleSearchSubmission(this);
        });
    }
}

function handleFormSubmission(form) {
    const formData = new FormData(form);
    const formType = form.getAttribute('data-form-type') || 'contact';
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="loading"></span> Sending...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        showToast('Success!', `Your ${formType} form has been submitted successfully.`, 'success');
        form.reset();
        form.classList.remove('was-validated');
        
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

function handleNewsletterSubmission(form) {
    const email = form.querySelector('input[type="email"]').value;
    
    if (email) {
        showToast('Subscribed!', 'Thank you for subscribing to our newsletter.', 'success');
        form.reset();
    }
}

function handleSearchSubmission(form) {
    const formData = new FormData(form);
    const searchParams = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        if (value) {
            searchParams.append(key, value);
        }
    }
    
    // Redirect to listings page with search parameters
    window.location.href = `listings.html?${searchParams.toString()}`;
}

// Property Filters
function initializePropertyFilters() {
    const filterForm = document.querySelector('.filter-form');
    const sortSelect = document.querySelector('.sort-select');
    const propertyGrid = document.querySelector('.property-grid');
    
    if (filterForm) {
        filterForm.addEventListener('change', applyFilters);
    }
    
    if (sortSelect) {
        sortSelect.addEventListener('change', applySorting);
    }
}

function applyFilters() {
    const formData = new FormData(document.querySelector('.filter-form'));
    const filters = {};
    
    for (let [key, value] of formData.entries()) {
        if (value) {
            filters[key] = value;
        }
    }
    
    // Filter properties based on criteria
    filterProperties(filters);
}

function applySorting() {
    const sortValue = document.querySelector('.sort-select').value;
    sortProperties(sortValue);
}

function filterProperties(filters) {
    // This would typically make an API call
    console.log('Applying filters:', filters);
    showToast('Filters Applied', 'Properties have been filtered based on your criteria.', 'info');
}

function sortProperties(sortBy) {
    // This would typically make an API call
    console.log('Sorting by:', sortBy);
    showToast('Sorted', `Properties sorted by ${sortBy}.`, 'info');
}

// Favorites System
function initializeFavorites() {
    document.addEventListener('click', function(e) {
        if (e.target.closest('.favorite-btn')) {
            e.preventDefault();
            toggleFavorite(e.target.closest('.favorite-btn'));
        }
    });
}

function toggleFavorite(btn) {
    const propertyId = btn.getAttribute('data-property-id');
    const isActive = btn.classList.contains('active');
    
    if (isActive) {
        btn.classList.remove('active');
        btn.innerHTML = '<i class="bi bi-heart"></i>';
        removeFromFavorites(propertyId);
        showToast('Removed', 'Property removed from favorites.', 'info');
    } else {
        btn.classList.add('active');
        btn.innerHTML = '<i class="bi bi-heart-fill"></i>';
        addToFavorites(propertyId);
        showToast('Added', 'Property added to favorites.', 'success');
    }
}

function addToFavorites(propertyId) {
    let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    if (!favorites.includes(propertyId)) {
        favorites.push(propertyId);
        localStorage.setItem('favorites', JSON.stringify(favorites));
    }
}

function removeFromFavorites(propertyId) {
    let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    favorites = favorites.filter(id => id !== propertyId);
    localStorage.setItem('favorites', JSON.stringify(favorites));
}

function getFavorites() {
    return JSON.parse(localStorage.getItem('favorites') || '[]');
}

// Toast Notifications
function showToast(title, message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <strong>${title}</strong><br>${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    // Add to toast container or create one
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    toastContainer.appendChild(toast);
    
    // Initialize and show toast
    if (typeof bootstrap !== 'undefined') {
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove toast element after it's hidden
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
}

// Utility Functions
function formatPrice(price) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(price);
}

function formatNumber(num) {
    return new Intl.NumberFormat('en-US').format(num);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Loading States
function showLoading(element) {
    element.innerHTML = '<div class="text-center p-4"><div class="loading"></div><p class="mt-2">Loading...</p></div>';
}

function hideLoading(element, content) {
    element.innerHTML = content;
}

// Error Handling
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    showToast('Error', 'Something went wrong. Please try again.', 'error');
});

// Export functions for use in other files
window.LuxEstate = {
    toggleChat,
    showToast,
    formatPrice,
    formatNumber,
    addToFavorites,
    removeFromFavorites,
    getFavorites,
    toggleFavorite
};
