<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login & Register - LuxEstate</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    
    <!-- AOS CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <style>
        /* Login/Register Page Specific Styles */
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 0;
        }
        
        .auth-card {
            max-width: 900px;
            width: 100%;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow-lg);
        }
        
        .auth-image {
            background-image: url('https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1350&q=80');
            background-size: cover;
            background-position: center;
            min-height: 100%;
        }
        
        .auth-tabs .nav-link {
            color: var(--bs-body-color);
            border: none;
            border-bottom: 2px solid transparent;
            border-radius: 0;
            padding: 1rem 1.5rem;
            font-weight: 600;
            transition: var(--transition);
        }
        
        .auth-tabs .nav-link.active {
            color: var(--luxury-gold);
            border-bottom-color: var(--luxury-gold);
            background: transparent;
        }
        
        .auth-tabs .nav-link:hover:not(.active) {
            border-bottom-color: var(--bs-border-color);
        }
        
        .social-login-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
            font-weight: 500;
            width: 100%;
            margin-bottom: 1rem;
        }
        
        .social-login-btn i {
            font-size: 1.25rem;
        }
        
        .social-login-btn.google {
            background: #fff;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .social-login-btn.facebook {
            background: #3b5998;
            color: white;
            border: 1px solid #3b5998;
        }
        
        .social-login-btn.apple {
            background: #000;
            color: white;
            border: 1px solid #000;
        }
        
        .social-login-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow);
        }
        
        .divider {
            display: flex;
            align-items: center;
            text-align: center;
            margin: 1.5rem 0;
        }
        
        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid var(--bs-border-color);
        }
        
        .divider span {
            padding: 0 1rem;
            color: var(--bs-secondary-color);
            font-size: 0.9rem;
        }
        
        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--bs-secondary-color);
            z-index: 10;
        }
        
        .form-floating > .password-toggle {
            top: 1.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top luxury-nav">
        <div class="container">
            <a class="navbar-brand fw-bold fs-3" href="index.html">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </a>
            
            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Desktop Navigation -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="listings.html">Properties</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.html">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">Contact</a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <!-- Theme Switcher -->
                    <div class="dropdown me-3">
                        <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-palette"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item theme-option" href="#" data-theme="light">
                                <i class="bi bi-sun"></i> Light
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="dark">
                                <i class="bi bi-moon"></i> Dark
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="luxury">
                                <i class="bi bi-gem"></i> Luxury
                            </a></li>
                        </ul>
                    </div>
                    
                    <a href="favorites.html" class="btn btn-outline-light btn-sm me-2">
                        <i class="bi bi-heart"></i> <span class="d-none d-md-inline">Favorites</span>
                    </a>
                    <a href="login.html" class="btn btn-warning btn-sm me-2">Login</a>
                    <a href="add-property.html" class="btn btn-outline-warning btn-sm">
                        <i class="bi bi-plus-circle"></i> <span class="d-none d-md-inline">Add Property</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Navigation Offcanvas -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="mobileNav">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="index.html">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="listings.html">Properties</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="about.html">About</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="blog.html">Blog</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="contact.html">Contact</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="favorites.html">Favorites</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.html">Dashboard</a>
                </li>
            </ul>
            <hr>
            <div class="d-grid gap-2">
                <a href="login.html" class="btn btn-warning">Login</a>
                <a href="add-property.html" class="btn btn-outline-warning">Add Property</a>
            </div>
        </div>
    </div>

    <!-- Login/Register Section -->
    <div class="auth-container">
        <div class="container">
            <div class="card auth-card">
                <div class="row g-0">
                    <!-- Image Column -->
                    <div class="col-lg-6 d-none d-lg-block">
                        <div class="auth-image"></div>
                    </div>
                    
                    <!-- Form Column -->
                    <div class="col-lg-6">
                        <div class="card-body p-4 p-lg-5">
                            <h2 class="fw-bold mb-4 text-center">Welcome to LuxEstate</h2>
                            
                            <!-- Auth Tabs -->
                            <ul class="nav nav-tabs auth-tabs mb-4" id="authTabs" role="tablist">
                                <li class="nav-item flex-fill text-center" role="presentation">
                                    <button class="nav-link active" id="login-tab" data-bs-toggle="tab" data-bs-target="#login-tab-pane" type="button" role="tab" aria-controls="login-tab-pane" aria-selected="true">Login</button>
                                </li>
                                <li class="nav-item flex-fill text-center" role="presentation">
                                    <button class="nav-link" id="register-tab" data-bs-toggle="tab" data-bs-target="#register-tab-pane" type="button" role="tab" aria-controls="register-tab-pane" aria-selected="false">Register</button>
                                </li>
                            </ul>
                            
                            <!-- Tab Content -->
                            <div class="tab-content" id="authTabsContent">
                                <!-- Login Tab -->
                                <div class="tab-pane fade show active" id="login-tab-pane" role="tabpanel" aria-labelledby="login-tab" tabindex="0">
                                    <!-- Social Login Buttons -->
                                    <div class="social-login mb-3">
                                        <button class="social-login-btn google">
                                            <i class="bi bi-google"></i> Continue with Google
                                        </button>
                                        <button class="social-login-btn facebook">
                                            <i class="bi bi-facebook"></i> Continue with Facebook
                                        </button>
                                        <button class="social-login-btn apple">
                                            <i class="bi bi-apple"></i> Continue with Apple
                                        </button>
                                    </div>
                                    
                                    <div class="divider">
                                        <span>OR LOGIN WITH EMAIL</span>
                                    </div>
                                    
                                    <!-- Login Form -->
                                    <form class="needs-validation" novalidate id="loginForm">
                                        <div class="mb-3">
                                            <label for="loginEmail" class="form-label">Email Address</label>
                                            <input type="email" class="form-control" id="loginEmail" required>
                                            <div class="invalid-feedback">Please enter a valid email address.</div>
                                        </div>
                                        
                                        <div class="mb-3 position-relative">
                                            <label for="loginPassword" class="form-label">Password</label>
                                            <input type="password" class="form-control" id="loginPassword" required>
                                            <span class="password-toggle" onclick="togglePasswordVisibility('loginPassword')">
                                                <i class="bi bi-eye"></i>
                                            </span>
                                            <div class="invalid-feedback">Please enter your password.</div>
                                        </div>
                                        
                                        <div class="d-flex justify-content-between mb-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="rememberMe">
                                                <label class="form-check-label" for="rememberMe">
                                                    Remember me
                                                </label>
                                            </div>
                                            <a href="#" class="text-decoration-none">Forgot password?</a>
                                        </div>
                                        
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-warning btn-lg">Login</button>
                                        </div>
                                    </form>
                                </div>
                                
                                <!-- Register Tab -->
                                <div class="tab-pane fade" id="register-tab-pane" role="tabpanel" aria-labelledby="register-tab" tabindex="0">
                                    <!-- Social Register Buttons -->
                                    <div class="social-login mb-3">
                                        <button class="social-login-btn google">
                                            <i class="bi bi-google"></i> Sign up with Google
                                        </button>
                                        <button class="social-login-btn facebook">
                                            <i class="bi bi-facebook"></i> Sign up with Facebook
                                        </button>
                                        <button class="social-login-btn apple">
                                            <i class="bi bi-apple"></i> Sign up with Apple
                                        </button>
                                    </div>
                                    
                                    <div class="divider">
                                        <span>OR REGISTER WITH EMAIL</span>
                                    </div>
                                    
                                    <!-- Register Form -->
                                    <form class="needs-validation" novalidate id="registerForm">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="firstName" class="form-label">First Name</label>
                                                <input type="text" class="form-control" id="firstName" required>
                                                <div class="invalid-feedback">Please enter your first name.</div>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="lastName" class="form-label">Last Name</label>
                                                <input type="text" class="form-control" id="lastName" required>
                                                <div class="invalid-feedback">Please enter your last name.</div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="registerEmail" class="form-label">Email Address</label>
                                            <input type="email" class="form-control" id="registerEmail" required>
                                            <div class="invalid-feedback">Please enter a valid email address.</div>
                                        </div>
                                        
                                        <div class="mb-3 position-relative">
                                            <label for="registerPassword" class="form-label">Password</label>
                                            <input type="password" class="form-control" id="registerPassword" required pattern=".{8,}" title="Password must be at least 8 characters">
                                            <span class="password-toggle" onclick="togglePasswordVisibility('registerPassword')">
                                                <i class="bi bi-eye"></i>
                                            </span>
                                            <div class="invalid-feedback">Password must be at least 8 characters.</div>
                                        </div>
                                        
                                        <div class="mb-3 position-relative">
                                            <label for="confirmPassword" class="form-label">Confirm Password</label>
                                            <input type="password" class="form-control" id="confirmPassword" required>
                                            <span class="password-toggle" onclick="togglePasswordVisibility('confirmPassword')">
                                                <i class="bi bi-eye"></i>
                                            </span>
                                            <div class="invalid-feedback">Passwords do not match.</div>
                                        </div>
                                        
                                        <div class="mb-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                                <label class="form-check-label" for="agreeTerms">
                                                    I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>
                                                </label>
                                                <div class="invalid-feedback">You must agree to the terms and conditions.</div>
                                            </div>
                                        </div>
                                        
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-warning btn-lg">Create Account</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- AOS JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/theme-switcher.js"></script>
    
    <script>
        // Toggle password visibility
        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            }
        }
        
        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            // Login form validation
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', function(event) {
                    if (!loginForm.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    } else {
                        event.preventDefault();
                        simulateLogin();
                    }
                    loginForm.classList.add('was-validated');
                });
            }
            
            // Register form validation
            const registerForm = document.getElementById('registerForm');
            if (registerForm) {
                const password = document.getElementById('registerPassword');
                const confirmPassword = document.getElementById('confirmPassword');
                
                // Check password match
                confirmPassword.addEventListener('input', function() {
                    if (password.value !== confirmPassword.value) {
                        confirmPassword.setCustomValidity('Passwords do not match');
                    } else {
                        confirmPassword.setCustomValidity('');
                    }
                });
                
                registerForm.addEventListener('submit', function(event) {
                    if (password.value !== confirmPassword.value) {
                        confirmPassword.setCustomValidity('Passwords do not match');
                    } else {
                        confirmPassword.setCustomValidity('');
                    }
                    
                    if (!registerForm.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    } else {
                        event.preventDefault();
                        simulateRegistration();
                    }
                    registerForm.classList.add('was-validated');
                });
            }
        });
        
        // Simulate login
        function simulateLogin() {
            const loginBtn = document.querySelector('#loginForm button[type="submit"]');
            const originalText = loginBtn.innerHTML;
            
            loginBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Logging in...';
            loginBtn.disabled = true;
            
            setTimeout(() => {
                if (window.LuxEstate && window.LuxEstate.showToast) {
                    window.LuxEstate.showToast('Login Successful', 'Welcome back to LuxEstate!', 'success');
                }
                
                // Redirect to dashboard
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1000);
            }, 2000);
        }
        
        // Simulate registration
        function simulateRegistration() {
            const registerBtn = document.querySelector('#registerForm button[type="submit"]');
            const originalText = registerBtn.innerHTML;
            
            registerBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Creating account...';
            registerBtn.disabled = true;
            
            setTimeout(() => {
                if (window.LuxEstate && window.LuxEstate.showToast) {
                    window.LuxEstate.showToast('Registration Successful', 'Your account has been created successfully!', 'success');
                }
                
                // Redirect to dashboard
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1000);
            }, 2000);
        }
    </script>
</body>
</html>
