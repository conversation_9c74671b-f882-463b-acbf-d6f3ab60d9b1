<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Favorites - LuxEstate</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    
    <!-- AOS CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top luxury-nav">
        <div class="container">
            <a class="navbar-brand fw-bold fs-3" href="index.html">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </a>
            
            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Desktop Navigation -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="listings.html">Properties</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.html">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">Contact</a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <!-- Theme Switcher -->
                    <div class="dropdown me-3">
                        <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-palette"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item theme-option" href="#" data-theme="light">
                                <i class="bi bi-sun"></i> Light
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="dark">
                                <i class="bi bi-moon"></i> Dark
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="luxury">
                                <i class="bi bi-gem"></i> Luxury
                            </a></li>
                        </ul>
                    </div>
                    
                    <a href="favorites.html" class="btn btn-outline-light btn-sm me-2 active">
                        <i class="bi bi-heart-fill"></i> <span class="d-none d-md-inline">Favorites</span>
                    </a>
                    <a href="login.html" class="btn btn-warning btn-sm me-2">Login</a>
                    <a href="add-property.html" class="btn btn-outline-warning btn-sm">
                        <i class="bi bi-plus-circle"></i> <span class="d-none d-md-inline">Add Property</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Navigation Offcanvas -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="mobileNav">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="index.html">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="listings.html">Properties</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="about.html">About</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="blog.html">Blog</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="contact.html">Contact</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="favorites.html">Favorites</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.html">Dashboard</a>
                </li>
            </ul>
            <hr>
            <div class="d-grid gap-2">
                <a href="login.html" class="btn btn-warning">Login</a>
                <a href="add-property.html" class="btn btn-outline-warning">Add Property</a>
            </div>
        </div>
    </div>

    <!-- Page Header -->
    <section class="py-5 mt-5 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold mb-3">
                        <i class="bi bi-heart-fill text-warning me-3"></i>My Favorites
                    </h1>
                    <p class="lead text-muted">Properties you've saved for later viewing</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <div class="d-flex align-items-center justify-content-lg-end gap-3">
                        <span class="text-muted">Total:</span>
                        <span class="fw-bold fs-4 text-warning" id="favoritesCount">0</span>
                        <span class="text-muted">properties</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Favorites Content -->
    <section class="py-5">
        <div class="container">
            <!-- Favorites Grid -->
            <div class="row" id="favoritesGrid">
                <!-- Favorites will be loaded dynamically -->
            </div>
            
            <!-- Empty State -->
            <div class="row" id="emptyState" style="display: none;">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-heart display-1 text-muted mb-4"></i>
                        <h3 class="fw-bold mb-3">No Favorites Yet</h3>
                        <p class="lead text-muted mb-4">Start exploring our luxury properties and save your favorites for easy access.</p>
                        <div class="d-flex gap-3 justify-content-center">
                            <a href="listings.html" class="btn btn-warning btn-lg">
                                <i class="bi bi-search me-2"></i>Browse Properties
                            </a>
                            <a href="index.html" class="btn btn-outline-secondary btn-lg">
                                <i class="bi bi-house me-2"></i>Go Home
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Favorites Actions -->
            <div class="row mt-5" id="favoritesActions" style="display: none;">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h5 class="fw-bold mb-2">Manage Your Favorites</h5>
                                    <p class="text-muted mb-0">Compare properties, schedule viewings, or share your favorites with others.</p>
                                </div>
                                <div class="col-md-4 text-md-end mt-3 mt-md-0">
                                    <div class="btn-group">
                                        <button class="btn btn-outline-warning" onclick="compareSelected()">
                                            <i class="bi bi-arrow-left-right me-2"></i>Compare
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="shareSelected()">
                                            <i class="bi bi-share me-2"></i>Share
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="clearAllFavorites()">
                                            <i class="bi bi-trash me-2"></i>Clear All
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="bi bi-building text-warning me-2"></i>LuxEstate
                    </h5>
                    <p class="text-muted">Your premier destination for luxury real estate worldwide. We connect discerning clients with exceptional properties.</p>
                    <div class="social-links">
                        <a href="#" class="text-warning me-3"><i class="bi bi-facebook fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-twitter fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-instagram fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-linkedin fs-4"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.html" class="text-muted text-decoration-none">Home</a></li>
                        <li><a href="listings.html" class="text-muted text-decoration-none">Properties</a></li>
                        <li><a href="about.html" class="text-muted text-decoration-none">About</a></li>
                        <li><a href="contact.html" class="text-muted text-decoration-none">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Services</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted text-decoration-none">Buy Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Sell Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Rent Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Property Management</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Contact Info</h6>
                    <div class="contact-info">
                        <p class="text-muted mb-2">
                            <i class="bi bi-geo-alt text-warning me-2"></i>
                            123 Luxury Avenue, Premium District, NY 10001
                        </p>
                        <p class="text-muted mb-2">
                            <i class="bi bi-telephone text-warning me-2"></i>
                            +****************
                        </p>
                        <p class="text-muted mb-2">
                            <i class="bi bi-envelope text-warning me-2"></i>
                            <EMAIL>
                        </p>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 LuxEstate. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-muted text-decoration-none me-3">Privacy Policy</a>
                    <a href="#" class="text-muted text-decoration-none">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating Chat Widget -->
    <div class="floating-chat" id="floatingChat">
        <div class="chat-toggle" onclick="toggleChat()">
            <i class="bi bi-chat-dots"></i>
        </div>
        <div class="chat-window" id="chatWindow">
            <div class="chat-header">
                <h6 class="mb-0">Need Help?</h6>
                <button class="btn-close btn-close-white" onclick="toggleChat()"></button>
            </div>
            <div class="chat-body">
                <p class="mb-2">Hi! How can we help you today?</p>
                <div class="quick-actions">
                    <button class="btn btn-sm btn-outline-light mb-2 w-100">Schedule Viewings</button>
                    <button class="btn btn-sm btn-outline-light mb-2 w-100">Compare Properties</button>
                    <button class="btn btn-sm btn-outline-light w-100">Speak to Agent</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- AOS JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/theme-switcher.js"></script>
    <script src="assets/js/property-data.js"></script>
    
    <script>
        // Favorites page functionality
        document.addEventListener('DOMContentLoaded', function() {
            loadFavorites();
        });
        
        function loadFavorites() {
            const favorites = getFavorites();
            const favoritesGrid = document.getElementById('favoritesGrid');
            const emptyState = document.getElementById('emptyState');
            const favoritesActions = document.getElementById('favoritesActions');
            const favoritesCount = document.getElementById('favoritesCount');
            
            if (favorites.length === 0) {
                favoritesGrid.style.display = 'none';
                favoritesActions.style.display = 'none';
                emptyState.style.display = 'block';
                favoritesCount.textContent = '0';
                return;
            }
            
            // Get property data
            if (window.PropertyData && window.PropertyData.sampleProperties) {
                const favoriteProperties = window.PropertyData.sampleProperties.filter(
                    property => favorites.includes(property.id.toString())
                );
                
                favoritesGrid.innerHTML = favoriteProperties.map(property => `
                    <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up">
                        ${window.PropertyData.createPropertyCard(property)}
                    </div>
                `).join('');
                
                favoritesCount.textContent = favoriteProperties.length;
                favoritesGrid.style.display = 'flex';
                favoritesActions.style.display = 'block';
                emptyState.style.display = 'none';
            }
        }
        
        function getFavorites() {
            return JSON.parse(localStorage.getItem('favorites') || '[]');
        }
        
        function compareSelected() {
            if (window.LuxEstate && window.LuxEstate.showToast) {
                window.LuxEstate.showToast('Compare Feature', 'Property comparison feature coming soon!', 'info');
            }
        }
        
        function shareSelected() {
            if (navigator.share) {
                navigator.share({
                    title: 'My Favorite Properties - LuxEstate',
                    text: 'Check out my favorite luxury properties on LuxEstate',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                navigator.clipboard.writeText(window.location.href).then(() => {
                    if (window.LuxEstate && window.LuxEstate.showToast) {
                        window.LuxEstate.showToast('Link Copied', 'Favorites page link copied to clipboard!', 'success');
                    }
                });
            }
        }
        
        function clearAllFavorites() {
            if (confirm('Are you sure you want to remove all properties from your favorites? This action cannot be undone.')) {
                localStorage.removeItem('favorites');
                loadFavorites();
                
                if (window.LuxEstate && window.LuxEstate.showToast) {
                    window.LuxEstate.showToast('Favorites Cleared', 'All properties have been removed from your favorites.', 'success');
                }
            }
        }
    </script>
</body>
</html>
