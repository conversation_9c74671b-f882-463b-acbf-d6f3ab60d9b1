"""
Serializers for the messages app.
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Conversation, Message, PropertyInquiry, Notification

User = get_user_model()

class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user serializer for messages."""
    
    full_name = serializers.ReadOnlyField(source='get_full_name')
    
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'full_name', 'avatar']

class MessageSerializer(serializers.ModelSerializer):
    """Serializer for messages."""
    
    sender = UserBasicSerializer(read_only=True)
    is_own_message = serializers.SerializerMethodField()
    
    class Meta:
        model = Message
        fields = [
            'id', 'conversation', 'sender', 'message_type', 'content',
            'attachment', 'is_read', 'read_at', 'is_deleted',
            'created_at', 'updated_at', 'is_own_message'
        ]
        read_only_fields = ['id', 'sender', 'is_read', 'read_at', 'created_at', 'updated_at']
    
    def get_is_own_message(self, obj):
        """Check if message belongs to current user."""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.sender == request.user
        return False

class ConversationSerializer(serializers.ModelSerializer):
    """Serializer for conversations."""
    
    participants = UserBasicSerializer(many=True, read_only=True)
    last_message = MessageSerializer(read_only=True)
    unread_count = serializers.SerializerMethodField()
    other_participant = serializers.SerializerMethodField()
    
    class Meta:
        model = Conversation
        fields = [
            'id', 'participants', 'related_property', 'subject', 'is_active',
            'last_message', 'unread_count', 'other_participant',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_unread_count(self, obj):
        """Get unread message count for current user."""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.messages.filter(
                is_read=False
            ).exclude(sender=request.user).count()
        return 0
    
    def get_other_participant(self, obj):
        """Get the other participant in conversation."""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            other_user = obj.get_other_participant(request.user)
            if other_user:
                return UserBasicSerializer(other_user).data
        return None

class ConversationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating conversations."""
    
    participant_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True
    )
    
    class Meta:
        model = Conversation
        fields = ['subject', 'related_property', 'participant_ids']
    
    def create(self, validated_data):
        """Create conversation with participants."""
        participant_ids = validated_data.pop('participant_ids')
        conversation = Conversation.objects.create(**validated_data)
        
        # Add participants
        participants = User.objects.filter(id__in=participant_ids)
        conversation.participants.set(participants)
        
        return conversation

class PropertyInquirySerializer(serializers.ModelSerializer):
    """Serializer for property inquiries."""
    
    property_title = serializers.ReadOnlyField(source='property.title')
    contact_name = serializers.ReadOnlyField(source='get_contact_name')
    contact_email = serializers.ReadOnlyField(source='get_contact_email')
    
    class Meta:
        model = PropertyInquiry
        fields = [
            'id', 'property', 'property_title', 'user', 'name', 'email', 'phone',
            'inquiry_type', 'subject', 'message', 'preferred_contact_method',
            'preferred_viewing_date', 'viewing_notes', 'is_responded',
            'responded_at', 'responded_by', 'contact_name', 'contact_email',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'user', 'is_responded', 'responded_at', 'responded_by',
            'created_at', 'updated_at'
        ]
    
    def create(self, validated_data):
        """Create inquiry with user and IP information."""
        request = self.context.get('request')
        if request:
            if request.user.is_authenticated:
                validated_data['user'] = request.user
                validated_data['name'] = request.user.get_full_name()
                validated_data['email'] = request.user.email
            
            # Get client IP
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                validated_data['ip_address'] = x_forwarded_for.split(',')[0]
            else:
                validated_data['ip_address'] = request.META.get('REMOTE_ADDR', '')
            
            validated_data['user_agent'] = request.META.get('HTTP_USER_AGENT', '')
            validated_data['referrer'] = request.META.get('HTTP_REFERER', '')
        
        return super().create(validated_data)

class NotificationSerializer(serializers.ModelSerializer):
    """Serializer for notifications."""
    
    class Meta:
        model = Notification
        fields = [
            'id', 'notification_type', 'title', 'message', 'property',
            'conversation', 'inquiry', 'is_read', 'read_at', 'action_url',
            'created_at'
        ]
        read_only_fields = ['id', 'created_at']
