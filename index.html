<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LuxEstate - Luxury Real Estate</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css">
    
    <!-- AOS CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top luxury-nav">
        <div class="container">
            <a class="navbar-brand fw-bold fs-3" href="index.html">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </a>
            
            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Desktop Navigation -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="listings.html">Properties</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.html">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">Contact</a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <!-- Theme Switcher -->
                    <div class="dropdown me-3">
                        <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-palette"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item theme-option" href="#" data-theme="light">
                                <i class="bi bi-sun"></i> Light
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="dark">
                                <i class="bi bi-moon"></i> Dark
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="luxury">
                                <i class="bi bi-gem"></i> Luxury
                            </a></li>
                        </ul>
                    </div>
                    
                    <a href="favorites.html" class="btn btn-outline-light btn-sm me-2">
                        <i class="bi bi-heart"></i> <span class="d-none d-md-inline">Favorites</span>
                    </a>
                    <a href="login.html" class="btn btn-warning btn-sm me-2">Login</a>
                    <a href="add-property.html" class="btn btn-outline-warning btn-sm">
                        <i class="bi bi-plus-circle"></i> <span class="d-none d-md-inline">Add Property</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Navigation Offcanvas -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="mobileNav">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="index.html">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="listings.html">Properties</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="about.html">About</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="blog.html">Blog</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="contact.html">Contact</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="favorites.html">Favorites</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.html">Dashboard</a>
                </li>
            </ul>
            <hr>
            <div class="d-grid gap-2">
                <a href="login.html" class="btn btn-warning">Login</a>
                <a href="add-property.html" class="btn btn-outline-warning">Add Property</a>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="hero-section position-relative overflow-hidden">
        <div class="hero-bg"></div>
        <div class="hero-overlay"></div>
        <div class="container position-relative">
            <div class="row min-vh-100 align-items-center">
                <div class="col-lg-8 mx-auto text-center text-white" data-aos="fade-up">
                    <h1 class="display-3 fw-bold mb-4">Find Your Dream Luxury Home</h1>
                    <p class="lead mb-5">Discover the finest properties in the most exclusive locations worldwide</p>
                    
                    <!-- Advanced Search Bar -->
                    <div class="search-container bg-white rounded-4 p-4 shadow-lg" data-aos="fade-up" data-aos-delay="200">
                        <form class="row g-3 align-items-end">
                            <div class="col-md-3">
                                <label class="form-label text-dark fw-semibold">Location</label>
                                <input type="text" class="form-control form-control-lg" placeholder="Enter city or area">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label text-dark fw-semibold">Property Type</label>
                                <select class="form-select form-select-lg">
                                    <option>Any Type</option>
                                    <option>House</option>
                                    <option>Apartment</option>
                                    <option>Villa</option>
                                    <option>Penthouse</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label text-dark fw-semibold">Bedrooms</label>
                                <select class="form-select form-select-lg">
                                    <option>Any</option>
                                    <option>1+</option>
                                    <option>2+</option>
                                    <option>3+</option>
                                    <option>4+</option>
                                    <option>5+</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label text-dark fw-semibold">Price Range</label>
                                <select class="form-select form-select-lg">
                                    <option>Any Price</option>
                                    <option>$500K - $1M</option>
                                    <option>$1M - $2M</option>
                                    <option>$2M - $5M</option>
                                    <option>$5M+</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-warning btn-lg w-100 fw-semibold">
                                    <i class="bi bi-search me-2"></i>Search
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Properties Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row mb-5">
                <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                    <h2 class="display-5 fw-bold mb-3">Featured Properties</h2>
                    <p class="lead text-muted">Handpicked luxury properties from our exclusive collection</p>
                </div>
            </div>
            
            <!-- Properties Carousel -->
            <div class="swiper featured-properties" data-aos="fade-up" data-aos-delay="200">
                <div class="swiper-wrapper">
                    <!-- Property cards will be dynamically loaded here -->
                </div>
                <div class="swiper-pagination"></div>
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-5 bg-dark text-white">
        <div class="container">
            <div class="row text-center">
                <div class="col-md-3 mb-4" data-aos="fade-up">
                    <div class="stat-item">
                        <h3 class="display-4 fw-bold text-warning mb-2">500+</h3>
                        <p class="lead">Luxury Properties</p>
                    </div>
                </div>
                <div class="col-md-3 mb-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="stat-item">
                        <h3 class="display-4 fw-bold text-warning mb-2">50+</h3>
                        <p class="lead">Cities Worldwide</p>
                    </div>
                </div>
                <div class="col-md-3 mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="stat-item">
                        <h3 class="display-4 fw-bold text-warning mb-2">1000+</h3>
                        <p class="lead">Happy Clients</p>
                    </div>
                </div>
                <div class="col-md-3 mb-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="stat-item">
                        <h3 class="display-4 fw-bold text-warning mb-2">15+</h3>
                        <p class="lead">Years Experience</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-5">
        <div class="container">
            <div class="row mb-5">
                <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                    <h2 class="display-5 fw-bold mb-3">What Our Clients Say</h2>
                    <p class="lead text-muted">Hear from satisfied clients who found their dream homes with us</p>
                </div>
            </div>
            
            <div class="swiper testimonials-swiper" data-aos="fade-up" data-aos-delay="200">
                <div class="swiper-wrapper">
                    <!-- Testimonial cards will be dynamically loaded here -->
                </div>
                <div class="swiper-pagination"></div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-5 bg-warning">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6" data-aos="fade-right">
                    <h3 class="fw-bold mb-3">Stay Updated with Luxury Properties</h3>
                    <p class="mb-0">Get exclusive access to new listings and market insights</p>
                </div>
                <div class="col-lg-6" data-aos="fade-left">
                    <form class="row g-2">
                        <div class="col-md-8">
                            <input type="email" class="form-control form-control-lg" placeholder="Enter your email">
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-dark btn-lg w-100">Subscribe</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="bi bi-building text-warning me-2"></i>LuxEstate
                    </h5>
                    <p class="text-muted">Your premier destination for luxury real estate worldwide. We connect discerning clients with exceptional properties.</p>
                    <div class="social-links">
                        <a href="#" class="text-warning me-3"><i class="bi bi-facebook fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-twitter fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-instagram fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-linkedin fs-4"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.html" class="text-muted text-decoration-none">Home</a></li>
                        <li><a href="listings.html" class="text-muted text-decoration-none">Properties</a></li>
                        <li><a href="about.html" class="text-muted text-decoration-none">About</a></li>
                        <li><a href="contact.html" class="text-muted text-decoration-none">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Services</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted text-decoration-none">Buy Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Sell Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Rent Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Property Management</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Contact Info</h6>
                    <div class="contact-info">
                        <p class="text-muted mb-2">
                            <i class="bi bi-geo-alt text-warning me-2"></i>
                            123 Luxury Avenue, Premium District, NY 10001
                        </p>
                        <p class="text-muted mb-2">
                            <i class="bi bi-telephone text-warning me-2"></i>
                            +****************
                        </p>
                        <p class="text-muted mb-2">
                            <i class="bi bi-envelope text-warning me-2"></i>
                            <EMAIL>
                        </p>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 LuxEstate. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-muted text-decoration-none me-3">Privacy Policy</a>
                    <a href="#" class="text-muted text-decoration-none">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating Chat Widget -->
    <div class="floating-chat" id="floatingChat">
        <div class="chat-toggle" onclick="toggleChat()">
            <i class="bi bi-chat-dots"></i>
        </div>
        <div class="chat-window" id="chatWindow">
            <div class="chat-header">
                <h6 class="mb-0">Need Help?</h6>
                <button class="btn-close btn-close-white" onclick="toggleChat()"></button>
            </div>
            <div class="chat-body">
                <p class="mb-2">Hi! How can we help you today?</p>
                <div class="quick-actions">
                    <button class="btn btn-sm btn-outline-light mb-2 w-100">Schedule a Viewing</button>
                    <button class="btn btn-sm btn-outline-light mb-2 w-100">Get Property Info</button>
                    <button class="btn btn-sm btn-outline-light w-100">Speak to Agent</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
    
    <!-- AOS JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/property-data.js"></script>
    <script src="assets/js/theme-switcher.js"></script>
    <script src="assets/js/main.js"></script>

</body>
</html>
