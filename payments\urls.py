"""
URLs for the payments app.
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

router = DefaultRouter()
router.register(r'payment-methods', views.PaymentMethodViewSet, basename='payment-method')
router.register(r'transactions', views.TransactionViewSet, basename='transaction')
router.register(r'subscriptions', views.SubscriptionViewSet, basename='subscription')
router.register(r'invoices', views.InvoiceViewSet, basename='invoice')
router.register(r'commissions', views.CommissionViewSet, basename='commission')

urlpatterns = [
    path('', include(router.urls)),
    path('stripe/webhook/', views.StripeWebhookView.as_view(), name='stripe-webhook'),
    path('create-payment-intent/', views.CreatePaymentIntentView.as_view(), name='create-payment-intent'),
    path('subscription-plans/', views.SubscriptionPlansView.as_view(), name='subscription-plans'),
]
