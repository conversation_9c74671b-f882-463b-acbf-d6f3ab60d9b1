"""
Admin configuration for messages app.
"""

from django.contrib import admin
from django.utils.html import format_html
from .models import Conversation, Message, PropertyInquiry, Notification

@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    """Admin for Conversation model."""
    
    list_display = ['subject', 'property', 'participants_display', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['subject', 'property__title']
    readonly_fields = ['created_at', 'updated_at', 'last_message_preview']
    filter_horizontal = ['participants']
    
    def participants_display(self, obj):
        """Display conversation participants."""
        return ', '.join([user.get_full_name() for user in obj.participants.all()[:3]])
    participants_display.short_description = 'Participants'
    
    def last_message_preview(self, obj):
        """Show last message preview."""
        last_message = obj.last_message
        if last_message:
            return format_html(
                '<strong>{}:</strong> {}',
                last_message.sender.get_full_name(),
                last_message.content[:100] + '...' if len(last_message.content) > 100 else last_message.content
            )
        return "No messages"
    last_message_preview.short_description = 'Last Message'

@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    """Admin for Message model."""
    
    list_display = [
        'conversation', 'sender', 'message_type', 'content_preview',
        'is_read', 'created_at'
    ]
    list_filter = ['message_type', 'is_read', 'is_deleted', 'created_at']
    search_fields = ['conversation__subject', 'sender__username', 'content']
    readonly_fields = ['created_at', 'updated_at', 'read_at']
    
    def content_preview(self, obj):
        """Show content preview."""
        if obj.message_type == 'text':
            return obj.content[:100] + '...' if len(obj.content) > 100 else obj.content
        elif obj.message_type == 'image':
            return format_html('<em>Image attachment</em>')
        elif obj.message_type == 'file':
            return format_html('<em>File attachment</em>')
        else:
            return format_html('<em>System message</em>')
    content_preview.short_description = 'Content'
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('conversation', 'sender')

@admin.register(PropertyInquiry)
class PropertyInquiryAdmin(admin.ModelAdmin):
    """Admin for PropertyInquiry model."""
    
    list_display = [
        'property', 'contact_name_display', 'inquiry_type',
        'is_responded', 'created_at'
    ]
    list_filter = [
        'inquiry_type', 'is_responded', 'preferred_contact_method',
        'created_at'
    ]
    search_fields = [
        'property__title', 'name', 'email', 'user__username',
        'subject', 'message'
    ]
    readonly_fields = [
        'ip_address', 'user_agent', 'referrer', 'created_at',
        'updated_at', 'responded_at'
    ]
    
    fieldsets = (
        ('Property & User', {
            'fields': ('property', 'user')
        }),
        ('Contact Information', {
            'fields': ('name', 'email', 'phone', 'preferred_contact_method')
        }),
        ('Inquiry Details', {
            'fields': (
                'inquiry_type', 'subject', 'message',
                'preferred_viewing_date', 'viewing_notes'
            )
        }),
        ('Response Status', {
            'fields': ('is_responded', 'responded_at', 'responded_by')
        }),
        ('Metadata', {
            'fields': ('ip_address', 'user_agent', 'referrer'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def contact_name_display(self, obj):
        """Display contact name."""
        return obj.get_contact_name()
    contact_name_display.short_description = 'Contact Name'
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related(
            'property', 'user', 'responded_by'
        )

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """Admin for Notification model."""
    
    list_display = [
        'user', 'notification_type', 'title', 'is_read', 'created_at'
    ]
    list_filter = ['notification_type', 'is_read', 'created_at']
    search_fields = ['user__username', 'title', 'message']
    readonly_fields = ['created_at', 'read_at']
    
    fieldsets = (
        ('User & Type', {
            'fields': ('user', 'notification_type')
        }),
        ('Content', {
            'fields': ('title', 'message', 'action_url')
        }),
        ('Related Objects', {
            'fields': ('property', 'conversation', 'inquiry'),
            'classes': ('collapse',)
        }),
        ('Status', {
            'fields': ('is_read', 'read_at')
        }),
        ('Timestamp', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related(
            'user', 'property', 'conversation', 'inquiry'
        )
