# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 04:22-0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: account/adapter.py:48
msgid "Username can not be used. Please use other username."
msgstr "無法使用此使用者名稱，請使用其他名稱。"

#: account/adapter.py:54
msgid "Too many failed login attempts. Try again later."
msgstr "登錄失敗次數過多，請稍後再試。"

#: account/adapter.py:56
msgid "A user is already registered with this email address."
msgstr "已經有人使用這一個電子郵件註冊了。"

#: account/adapter.py:57
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "目前密碼"

#: account/adapter.py:308
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "密碼長度至少要有 {0} 個字元。"

#: account/apps.py:9
msgid "Accounts"
msgstr "帳號"

#: account/forms.py:58 account/forms.py:432
msgid "You must type the same password each time."
msgstr "每次輸入的密碼必須相同"

#: account/forms.py:90 account/forms.py:397 account/forms.py:522
#: account/forms.py:658
msgid "Password"
msgstr "密碼"

#: account/forms.py:91
msgid "Remember Me"
msgstr "記住我"

#: account/forms.py:95
msgid "This account is currently inactive."
msgstr "此帳號目前沒有啟用。"

#: account/forms.py:97
msgid "The email address and/or password you specified are not correct."
msgstr "您提供的電子郵件地址或密碼不正確。"

#: account/forms.py:100
msgid "The username and/or password you specified are not correct."
msgstr "您提供的使用者名稱或密碼不正確。"

#: account/forms.py:111 account/forms.py:271 account/forms.py:459
#: account/forms.py:540
msgid "Email address"
msgstr "電子郵件地址"

#: account/forms.py:115 account/forms.py:316 account/forms.py:456
#: account/forms.py:535
msgid "Email"
msgstr "E-mail"

#: account/forms.py:118 account/forms.py:121 account/forms.py:261
#: account/forms.py:264
msgid "Username"
msgstr "使用者名稱"

#: account/forms.py:131
msgid "Username or email"
msgstr "使用者名稱或電子郵件"

#: account/forms.py:134
msgctxt "field label"
msgid "Login"
msgstr "登入"

#: account/forms.py:307
#, fuzzy
#| msgid "Email (optional)"
msgid "Email (again)"
msgstr "E-mail (可不填)"

#: account/forms.py:311
#, fuzzy
#| msgid "email confirmation"
msgid "Email address confirmation"
msgstr "電子郵件確認"

#: account/forms.py:319
msgid "Email (optional)"
msgstr "E-mail (可不填)"

#: account/forms.py:368
#, fuzzy
#| msgid "You must type the same password each time."
msgid "You must type the same email each time."
msgstr "每次輸入的密碼必須相同"

#: account/forms.py:401 account/forms.py:523
msgid "Password (again)"
msgstr "密碼 (再一次)"

#: account/forms.py:470
msgid "This email address is already associated with this account."
msgstr "此電子郵件已與這個帳號連結了。"

#: account/forms.py:472
#, fuzzy, python-format
#| msgid "Your account has no verified email address."
msgid "You cannot add more than %d email addresses."
msgstr "您的帳號下沒有驗證過的電子郵件地址。"

#: account/forms.py:503
msgid "Current Password"
msgstr "目前密碼"

#: account/forms.py:505 account/forms.py:607
msgid "New Password"
msgstr "新密碼"

#: account/forms.py:506 account/forms.py:608
msgid "New Password (again)"
msgstr "新密碼 (再一次)"

#: account/forms.py:514
msgid "Please type your current password."
msgstr "請輸入您目前的密碼"

#: account/forms.py:552
msgid "The email address is not assigned to any user account"
msgstr "還沒有其他帳號使用這個電子郵件地址"

#: account/forms.py:628
msgid "The password reset token was invalid."
msgstr ""

#: account/models.py:21
msgid "user"
msgstr "使用者"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "電子郵件地址"

#: account/models.py:28
msgid "verified"
msgstr "已驗證"

#: account/models.py:29
msgid "primary"
msgstr "主要的"

#: account/models.py:35
msgid "email addresses"
msgstr "電子郵件地址"

#: account/models.py:141
msgid "created"
msgstr "以建立"

#: account/models.py:142
msgid "sent"
msgstr "已送出"

#: account/models.py:143 socialaccount/models.py:62
msgid "key"
msgstr "key"

#: account/models.py:148
msgid "email confirmation"
msgstr "電子郵件確認"

#: account/models.py:149
msgid "email confirmations"
msgstr "電子郵件確認"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:12
msgid "Code"
msgstr ""

#: mfa/forms.py:29
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:30
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"已經有一個帳號與此電子郵件連結了，請先登入該帳號，然後連接你的 %s 帳號。"

#: socialaccount/adapter.py:136
msgid "Your account has no password set up."
msgstr "您的帳號沒有設置密碼。"

#: socialaccount/adapter.py:143
msgid "Your account has no verified email address."
msgstr "您的帳號下沒有驗證過的電子郵件地址。"

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "社群帳號"

#: socialaccount/models.py:36 socialaccount/models.py:97
msgid "provider"
msgstr "提供者"

#: socialaccount/models.py:45
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "提供者"

#: socialaccount/models.py:49
msgid "name"
msgstr "名稱"

#: socialaccount/models.py:51
msgid "client id"
msgstr "client id"

#: socialaccount/models.py:53
msgid "App ID, or consumer key"
msgstr "App ID, or consumer key"

#: socialaccount/models.py:56
msgid "secret key"
msgstr "secret key"

#: socialaccount/models.py:59
msgid "API secret, client secret, or consumer secret"
msgstr "API secret, client secret, or consumer secret"

#: socialaccount/models.py:62
msgid "Key"
msgstr "Key"

#: socialaccount/models.py:81
msgid "social application"
msgstr "社群應用程式"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "社群應用程式"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "最後一次登入"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "加入日期"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "額外資料"

#: socialaccount/models.py:125
msgid "social account"
msgstr "社群帳號"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "社群帳號"

#: socialaccount/models.py:160
msgid "token"
msgstr ""

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr ""

#: socialaccount/models.py:165
msgid "token secret"
msgstr ""

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr ""

#: socialaccount/models.py:169
msgid "expires at"
msgstr "過期日"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "社群應用程式 Token"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "社群應用程式 Token"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr ""

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Invalid response while obtaining request token from \"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Invalid response while obtaining access token from \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "No request token saved for \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "No access token saved for \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "無權訪問私有資源 \"%s\"。"

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Invalid response while obtaining request token from \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:8
msgid "Account Inactive"
msgstr "帳號未啟用"

#: templates/account/account_inactive.html:10
msgid "This account is inactive."
msgstr "這個帳號未啟用"

#: templates/account/base.html:16
msgid "Messages:"
msgstr ""

#: templates/account/base.html:26
msgid "Menu:"
msgstr ""

#: templates/account/base.html:29 templates/account/email_change.html:31
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "E-mail"

#: templates/account/base.html:30 templates/account/logout.html:5
#: templates/account/logout.html:8 templates/account/logout.html:17
msgid "Sign Out"
msgstr "登出"

#: templates/account/base.html:32 templates/account/login.html:6
#: templates/account/login.html:10 templates/account/login.html:43
#: templates/mfa/authenticate.html:4 templates/mfa/authenticate.html:16
#: templates/socialaccount/login.html:4
msgid "Sign In"
msgstr "登入"

#: templates/account/base.html:33 templates/account/signup.html:8
#: templates/account/signup.html:18 templates/socialaccount/signup.html:8
#: templates/socialaccount/signup.html:19
msgid "Sign Up"
msgstr "註冊"

#: templates/account/email.html:5 templates/account/email.html:8
msgid "Email Addresses"
msgstr "電子郵件地址"

#: templates/account/email.html:10
msgid "The following email addresses are associated with your account:"
msgstr "下列電子郵件已與你的帳號連結："

#: templates/account/email.html:24
msgid "Verified"
msgstr "已驗證"

#: templates/account/email.html:26
msgid "Unverified"
msgstr "未驗證"

#: templates/account/email.html:28
msgid "Primary"
msgstr "主要的"

#: templates/account/email.html:34
msgid "Make Primary"
msgstr "設為主要的"

#: templates/account/email.html:35 templates/account/email_change.html:21
msgid "Re-send Verification"
msgstr "重寄驗証信"

#: templates/account/email.html:36 templates/socialaccount/connections.html:35
msgid "Remove"
msgstr "移除"

#: templates/account/email.html:47
msgid "Add Email Address"
msgstr "增加電子郵件"

#: templates/account/email.html:52
msgid "Add Email"
msgstr "增加電子郵件"

#: templates/account/email.html:62
msgid "Do you really want to remove the selected email address?"
msgstr "您真的要移除所選擇電子郵件嗎？"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr ""

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "User %(user_display)s at %(site_name)s has given this as an email "
#| "address.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s\n"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"網站%(site_name)s上的使用者%(user_display)s將此設為他的電子郵件地址。\n"
"\n"
"為了確認這是正確的，請開啟這個連結： %(activate_url)s\n"

#: templates/account/email/email_confirmation_subject.txt:3
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Please Confirm Your Email Address"
msgstr "確認電子郵件"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account at %(site_domain)s.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"您會收到這封信是因為您或是某人在 %(site_domain)s 這個網站上要求重設您帳號的密"
"碼。\n"
"若您沒有要求我們重設密碼，請您直接忽略這封信。若要重設您的密碼，請點擊下面的"
"連結。"

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "提醒您，您的使用者名稱是 %(username)s 。"

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "密碼重設電子郵件"

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account at %(site_domain)s.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"您會收到這封信是因為您或是某人在 %(site_domain)s 這個網站上要求重設您帳號的密"
"碼。\n"
"若您沒有要求我們重設密碼，請您直接忽略這封信。若要重設您的密碼，請點擊下面的"
"連結。"

#: templates/account/email_change.html:4 templates/account/email_change.html:7
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "電子郵件地址"

#: templates/account/email_change.html:11
#, fuzzy
#| msgid "The following email addresses are associated with your account:"
msgid "The following email address is associated with your account:"
msgstr "下列電子郵件已與你的帳號連結："

#: templates/account/email_change.html:16
#, fuzzy
#| msgid "Your email address has already been verified"
msgid "Your email address is still pending verification:"
msgstr "Je e-mail adres is al geverifieerd"

#: templates/account/email_change.html:27
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Change Email Address"
msgstr "確認電子郵件"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "確認電子郵件"

#: templates/account/email_confirm.html:17
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"請確認<a href=\"mailto:%(email)s\">%(email)s</a>是否是使用者 "
"%(user_display)s 的電子郵件地址。"

#: templates/account/email_confirm.html:21
#: templates/account/reauthenticate.html:19
msgid "Confirm"
msgstr "確認"

#: templates/account/email_confirm.html:24
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "這個社群網站帳號已經與另一個帳號連結過了。"

#: templates/account/email_confirm.html:31
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a href="
#| "\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"電子郵件確認連結已經過期或失效了，請點擊 <a href=\"%(email_url)s\"> 以要求發"
"送新的電子郵件確認信。"

#: templates/account/login.html:15
#, fuzzy, python-format
msgid ""
"Please sign in with one\n"
"of your existing third party accounts. Or, <a href=\"%(signup_url)s\">sign "
"up</a>\n"
"for a %(site_name)s account and sign in below:"
msgstr ""
"請用您的第三方帳號登入。\n"
"或者<a href=\"%(signup_url)s\">註冊</a> \n"
"一個 %(site_name)s帳號後登入："

#: templates/account/login.html:25
msgid "or"
msgstr "或"

#: templates/account/login.html:32
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"<a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"若你沒有帳號，請先\n"
"<a href=\"%(signup_url)s\">註冊</a> 。"

#: templates/account/login.html:42 templates/account/password_change.html:14
msgid "Forgot Password?"
msgstr "忘記密碼了？"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "您確定要登出嗎？"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "您不能移除您的主要的電子郵件地址 (%(email)s) 。"

#: templates/account/messages/email_confirmation_sent.txt:2
#, fuzzy, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "確認信已發至 %(email)s 。"

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "您以確認電子郵件地址 %(email)s 。 "

#: templates/account/messages/email_deleted.txt:2
#, fuzzy, python-format
msgid "Removed email address %(email)s."
msgstr "電子郵件地址 %(email)s 已刪除。"

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "成功以 %(name)s..的身份登入。"

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "您已登出。"

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "密碼修改完成。"

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "密碼設定完成。"

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "已設定好主要的電子郵件地址。"

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "您的主要電子郵件位址必須被驗證過。"

#: templates/account/password_change.html:5
#: templates/account/password_change.html:8
#: templates/account/password_change.html:13
#: templates/account/password_reset_from_key.html:4
#: templates/account/password_reset_from_key.html:7
#: templates/account/password_reset_from_key_done.html:4
#: templates/account/password_reset_from_key_done.html:7
msgid "Change Password"
msgstr "修改密碼"

#: templates/account/password_reset.html:6
#: templates/account/password_reset.html:10
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:9
msgid "Password Reset"
msgstr "密碼重設"

#: templates/account/password_reset.html:15
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"忘記您的密碼了嗎？ 請在下方輸入您的電子郵件，我們會發送一封電子郵件給您，以便"
"重新設定您的密碼。"

#: templates/account/password_reset.html:20
msgid "Reset My Password"
msgstr "重設我的密碼"

#: templates/account/password_reset.html:23
msgid "Please contact us if you have any trouble resetting your password."
msgstr "如果在重設密碼時碰到問題，請與我們聯絡。"

#: templates/account/password_reset_done.html:15
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"我們剛剛寄了一封電子郵件確認信給您，\n"
"請點擊郵件中的連結。您在數分鐘內尚無法收到郵件，請與我們聯絡。"

#: templates/account/password_reset_from_key.html:7
msgid "Bad Token"
msgstr "Bad Token"

#: templates/account/password_reset_from_key.html:11
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"密碼重設連結已失效，可能是因為該連結已經被人用過了，請重新申請<a href="
"\"%(passwd_reset_url)s\">重設密碼</a>。"

#: templates/account/password_reset_from_key.html:16
msgid "change password"
msgstr "修改密碼"

#: templates/account/password_reset_from_key_done.html:8
msgid "Your password is now changed."
msgstr "您的密碼已變更。"

#: templates/account/password_set.html:5 templates/account/password_set.html:8
#: templates/account/password_set.html:13
msgid "Set Password"
msgstr "設定密碼"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "確認電子郵件"

#: templates/account/reauthenticate.html:11
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""

#: templates/account/signup.html:5 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "註冊"

#: templates/account/signup.html:10
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "已有帳號了嗎？請<a href=\"%(login_url)s\">登入</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:8
msgid "Sign Up Closed"
msgstr "註冊未開放"

#: templates/account/signup_closed.html:10
msgid "We are sorry, but the sign up is currently closed."
msgstr "很抱歉，目前不開放註冊。"

#: templates/account/snippets/already_logged_in.html:5
msgid "Note"
msgstr "注意"

#: templates/account/snippets/already_logged_in.html:5
#, python-format
msgid "you are already logged in as %(user_display)s."
msgstr "您已經以 %(user_display)s 的身份登入了。"

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "警告："

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"您尚未設定任何電子郵件。建議您最好設定一個電子郵件，以便您接收通知或重新設定"
"密碼等等。"

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:8
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:8
msgid "Verify Your Email Address"
msgstr "驗證您的電子郵件地址"

#: templates/account/verification_sent.html:10
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. Please contact us if you do not receive "
#| "it within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"我們剛剛寄了一封電子郵件確認信給您，請點擊郵件中的連結以完成註冊流程。若您在"
"數分鐘內尚無法收到郵件，請與我們聯絡。"

#: templates/account/verified_email_required.html:12
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"此網站的這部分功能需要驗證您的身份，\n"
"因此我們需要確認您的電子郵件地址。"

#: templates/account/verified_email_required.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"我們剛剛寄了一封電子郵件確認信給您，\n"
"請點擊郵件中的連結。您在數分鐘內尚無法收到郵件，請與我們聯絡。"

#: templates/account/verified_email_required.html:20
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>注意：</strong> 您仍能<a href=\"%(email_url)s\">修改您的電子郵件地址 "
"</a>."

#: templates/mfa/authenticate.html:7 templates/mfa/index.html:4
#: templates/mfa/index.html:7
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:9
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:9 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:11
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/deactivate_form.html:11
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:18
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:21 templates/mfa/totp/activate_form.html:16
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:4
#: templates/mfa/recovery_codes/index.html:4
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:30 templates/mfa/recovery_codes/index.html:6
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""

#: templates/mfa/index.html:34
msgid "View codes"
msgstr ""

#: templates/mfa/index.html:37 templates/mfa/recovery_codes/index.html:16
msgid "Download codes"
msgstr ""

#: templates/mfa/index.html:40 templates/mfa/recovery_codes/index.html:20
msgid "Generate new codes"
msgstr ""

#: templates/mfa/index.html:44
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:6
msgid ""
"You are about to generate a new set of recovery codes for your account. This "
"action will invalidate your existing codes. Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "Generate"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:9
msgid "Authenticator secret"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:4
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:6
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/openid/login.html:9
msgid "OpenID Sign In"
msgstr "OpenID 登入"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:8
msgid "Social Network Login Failure"
msgstr "社群網路登入失敗"

#: templates/socialaccount/authentication_error.html:10
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr "當嘗試用您的社群網路帳號登入時發生錯誤。"

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:8
msgid "Account Connections"
msgstr "帳號連結"

#: templates/socialaccount/connections.html:11
msgid ""
"You can sign in to your account using any of the following third party "
"accounts:"
msgstr "您可以使用下列任何第三方帳號登入您的帳號："

#: templates/socialaccount/connections.html:43
msgid ""
"You currently have no social network accounts connected to this account."
msgstr "您目前沒有任何社群網路帳號與此帳號連結。"

#: templates/socialaccount/connections.html:46
msgid "Add a 3rd Party Account"
msgstr "增加一個第三方帳號"

#: templates/socialaccount/login.html:8
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:10
#, python-format
msgid "You are about to connect a new third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:12
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:14
#, python-format
msgid "You are about to sign in using a third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:19
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "登入取消了"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"您決定不繼續登入這一個網站。若這是一個失誤，請<a href=\"%(login_url)s\">由此"
"</a>重新登入。"

#: templates/socialaccount/messages/account_connected.txt:2
#, fuzzy
msgid "The social account has been connected."
msgstr "社群網站帳號已連結。"

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "這個社群網站帳號已經與另一個帳號連結過了。"

#: templates/socialaccount/messages/account_disconnected.txt:2
#, fuzzy
msgid "The social account has been disconnected."
msgstr "社群網站帳號已斷開連結。"

#: templates/socialaccount/signup.html:10
#, fuzzy, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"您將使用 %(provider_name)s 這個帳號登入\n"
" %(site_name)s 這個網站。最後一步，請填完下列表單："

#~ msgid "This email address is already associated with another account."
#~ msgstr "此電子郵件已經與別的帳號連結了。"

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "我們已經寄了一封電子郵件給您，如果數分鐘內您沒有收到，請與我們聯絡。"

#~ msgid "Account"
#~ msgstr "帳號"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "您提供的帳號或密碼不正確。"

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "使用者名稱只能包含字母，數字及 @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "這個使用者名稱已經有人用了，請換一個。"

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "登入"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "您以確認<a href=\"mailto:%(email)s\">%(email)s</a>是使用者%(user_display)s"
#~ "的電子郵件地址。"

#~ msgid "Thanks for using our site!"
#~ msgstr "感謝您使用我們的網站！"

#~ msgid "Confirmation email sent to %(email)s"
#~ msgstr "确认e-mail已发往 %(email)s"

#~ msgid "Delete Password"
#~ msgstr "删除密码"

#~ msgid ""
#~ "You may delete your password since you are currently logged in using "
#~ "OpenID."
#~ msgstr "您当前使用OpenID登录，因此您可以删除你的密码。"

#~ msgid "delete my password"
#~ msgstr "删除我的密码"

#~ msgid "Password Deleted"
#~ msgstr "密码已删除"

#~ msgid "Your password has been deleted."
#~ msgstr "您的密码已被删除。"

#~ msgid ""
#~ "If you have any trouble resetting your password, contact us at <a href="
#~ "\"mailto:%(CONTACT_EMAIL)s\">%(CONTACT_EMAIL)s</a>."
#~ msgstr ""
#~ "Als je problemen hebt je wachtwoord opnieuw in te stellen, neem dan "
#~ "contact op met <a href=\"mailto:%(CONTACT_EMAIL)s\">%(CONTACT_EMAIL)s</a>."

#~ msgid "Invalid confirmation key."
#~ msgstr "Ongeldige bevestigingssleutel."

#~ msgid "OpenID"
#~ msgstr "OpenID"

#~ msgid "Already have an account?"
#~ msgstr "Heb je al een account?"

#~ msgid "Sign in"
#~ msgstr "Aanmelden"

#~ msgid "Language"
#~ msgstr "Taal"

#~ msgid "Pinax can be used in your preferred language."
#~ msgstr "Deze site kan in jouw voorkeurstaal gebruikt worden."

#~ msgid "Change my language"
#~ msgstr "Verander mijn taal"

#~ msgid "Timezone"
#~ msgstr "Tijdzone"

#, fuzzy
#~ msgid ""
#~ "You're receiving this e-mail because you requested a password reset\n"
#~ "for your user account at Pinax.\n"
#~ "\n"
#~ "Your new password is: %(new_password)s\n"
#~ "\n"
#~ "Your username, in case you've forgotten: %(username)s\n"
#~ "\n"
#~ "You should log in as soon as possible and change your password.\n"
#~ "\n"
#~ "Thanks for using our site!\n"
#~ msgstr ""
#~ "Je ontvangt deze mail omdat er een verzoek is ingelegd om het wachtwoord\n"
#~ "behorende bij je %(site_name)s account opnieuw in te stellen.\n"
#~ "\n"
#~ "Je nieuwe wachtwoord is: %(new_password)s\n"
#~ "\n"
#~ "Je gebruikersnaam, voor het geval je die vergeten bent, is: %(username)s\n"
#~ "\n"
#~ "Je moet zo snel mogelijk inloggen en bovenstaand wachtwoord veranderen.\n"
#~ "\n"
#~ "Bedankt voor het gebruik van onze site!\n"

#~ msgid "If checked you will stay logged in for 3 weeks"
#~ msgstr "Bij 'Onthouden' blijf je ingelogd gedurende 3 weken"

#~ msgid "Timezone successfully updated."
#~ msgstr "Tijdzone gewijzigd."

#~ msgid "Language successfully updated."
#~ msgstr "Taal gewijzigd."

#~ msgid "None"
#~ msgstr "Geen"

#~ msgid "add"
#~ msgstr "Voeg toe"

#~ msgid "Log In"
#~ msgstr "Inloggen"

#~ msgid "Log in"
#~ msgstr "Inloggen"

#~ msgid "Logout"
#~ msgstr "Afmelden"

#~ msgid ""
#~ "When you receive the new password, you should <a href=\"%(login_url)s"
#~ "\">log in</a> and change it as soon as possible."
#~ msgstr ""
#~ "Zodra je het nieuwe wachtwoord ontvangen hebt moet je zo snel mogelijk <a "
#~ "href=\"%(login_url)s\">inloggen</a> en het wachtwoord wijzigen."

#~ msgid "You are already logged in."
#~ msgstr "Je bent al ingelogd."

#~ msgid ""
#~ "By clicking \"Sign Up\", you are indicating that you have read and agree "
#~ "to the <a href=\"%(terms_url)s\">Terms of Use</a> and <a href="
#~ "\"%(privacy_url)s\">Privacy Policy</a>."
#~ msgstr ""
#~ "Door te registreren geef je aan dat je de <a href=\"%(terms_url)s"
#~ "\">gebruiksvoorwaarden</a> en de <a href=\"%(privacy_url)s\">privacy "
#~ "policy</a> gelezen hebt en ermee akkoord gaat."

#~ msgid ""
#~ "If you have any trouble creating your account, contact us at <a href="
#~ "\"mailto:%(contact_email)s\">%(contact_email)s</a>."
#~ msgstr ""
#~ "Als je problemen hebt om een account aan te maken, neem dan contact op "
#~ "met <a href=\"mailto:%(contact_email)s\">%(contact_email)s</a>."

#~ msgid "Log in &raquo;"
#~ msgstr "Inloggen"
