// ===== ADD PROPERTY PAGE FUNCTIONALITY =====

let uploadedImages = [];
const maxImages = 10;
const maxFileSize = 5 * 1024 * 1024; // 5MB

// Initialize add property page
document.addEventListener('DOMContentLoaded', function() {
    initializeImageUpload();
    initializeFormValidation();
    bindAddPropertyEvents();
});

function initializeImageUpload() {
    const uploadArea = document.getElementById('imageUploadArea');
    const fileInput = document.getElementById('imageInput');
    
    if (!uploadArea || !fileInput) return;
    
    // Click to upload
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });
    
    // File input change
    fileInput.addEventListener('change', handleFileSelect);
    
    // Drag and drop events
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
}

function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    e.currentTarget.classList.remove('dragover');
    
    const files = Array.from(e.dataTransfer.files);
    processFiles(files);
}

function handleFileSelect(e) {
    const files = Array.from(e.target.files);
    processFiles(files);
    e.target.value = ''; // Reset input
}

function processFiles(files) {
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    
    if (uploadedImages.length + imageFiles.length > maxImages) {
        showToast('Too Many Images', `Maximum ${maxImages} images allowed`, 'error');
        return;
    }
    
    imageFiles.forEach(file => {
        if (file.size > maxFileSize) {
            showToast('File Too Large', `${file.name} is larger than 5MB`, 'error');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            const imageData = {
                id: Date.now() + Math.random(),
                file: file,
                dataUrl: e.target.result,
                name: file.name
            };
            
            uploadedImages.push(imageData);
            renderUploadedImages();
        };
        reader.readAsDataURL(file);
    });
}

function renderUploadedImages() {
    const container = document.getElementById('uploadedImages');
    if (!container) return;
    
    container.innerHTML = uploadedImages.map(image => `
        <div class="uploaded-image">
            <img src="${image.dataUrl}" alt="${image.name}">
            <button type="button" class="remove-image" onclick="removeImage('${image.id}')">
                <i class="bi bi-x"></i>
            </button>
        </div>
    `).join('');
    
    // Update upload area text
    const uploadArea = document.getElementById('imageUploadArea');
    if (uploadArea) {
        const remainingSlots = maxImages - uploadedImages.length;
        if (remainingSlots === 0) {
            uploadArea.style.display = 'none';
        } else {
            uploadArea.style.display = 'block';
            uploadArea.querySelector('small').textContent = `${remainingSlots} more images allowed, 5MB each`;
        }
    }
}

function removeImage(imageId) {
    uploadedImages = uploadedImages.filter(img => img.id !== imageId);
    renderUploadedImages();
}

function initializeFormValidation() {
    const form = document.getElementById('addPropertyForm');
    if (!form) return;
    
    form.addEventListener('submit', function(event) {
        event.preventDefault();
        event.stopPropagation();
        
        if (form.checkValidity() && validateCustomFields()) {
            submitProperty();
        } else {
            showValidationErrors();
        }
        
        form.classList.add('was-validated');
    });
}

function validateCustomFields() {
    let isValid = true;
    
    // Validate images
    if (uploadedImages.length === 0) {
        showToast('Images Required', 'Please upload at least one image of your property', 'error');
        isValid = false;
    }
    
    // Validate price
    const price = document.getElementById('propertyPrice').value;
    if (price && parseInt(price) < 1000) {
        showToast('Invalid Price', 'Property price must be at least $1,000', 'error');
        isValid = false;
    }
    
    return isValid;
}

function showValidationErrors() {
    const firstInvalidField = document.querySelector('.form-control:invalid, .form-select:invalid');
    if (firstInvalidField) {
        firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        firstInvalidField.focus();
    }
}

function bindAddPropertyEvents() {
    // Price formatting
    const priceInput = document.getElementById('propertyPrice');
    if (priceInput) {
        priceInput.addEventListener('input', function() {
            // Remove non-numeric characters except decimal point
            this.value = this.value.replace(/[^0-9.]/g, '');
        });
    }
    
    // Area formatting
    const areaInput = document.getElementById('propertyArea');
    if (areaInput) {
        areaInput.addEventListener('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    }
    
    // Phone formatting
    const phoneInput = document.getElementById('contactPhone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.length >= 6) {
                value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
            } else if (value.length >= 3) {
                value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
            }
            this.value = value;
        });
    }
    
    // Save as draft button
    const draftBtn = document.querySelector('button[type="button"]');
    if (draftBtn && draftBtn.textContent.includes('Save as Draft')) {
        draftBtn.addEventListener('click', saveAsDraft);
    }
}

function submitProperty() {
    const formData = collectFormData();
    
    // Show loading state
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Submitting...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Show success message
        showToast('Property Submitted!', 'Your property has been submitted for review. We\'ll contact you within 24 hours.', 'success');
        
        // Reset form
        document.getElementById('addPropertyForm').reset();
        uploadedImages = [];
        renderUploadedImages();
        
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        // Redirect to dashboard after delay
        setTimeout(() => {
            window.location.href = 'dashboard.html';
        }, 2000);
    }, 3000);
}

function saveAsDraft() {
    const formData = collectFormData();
    
    // Save to localStorage
    localStorage.setItem('propertyDraft', JSON.stringify({
        ...formData,
        savedAt: new Date().toISOString()
    }));
    
    showToast('Draft Saved', 'Your property draft has been saved locally.', 'success');
}

function collectFormData() {
    const form = document.getElementById('addPropertyForm');
    const formData = new FormData(form);
    
    const data = {
        title: document.getElementById('propertyTitle').value,
        type: document.getElementById('propertyType').value,
        price: document.getElementById('propertyPrice').value,
        area: document.getElementById('propertyArea').value,
        location: document.getElementById('propertyLocation').value,
        description: document.getElementById('propertyDescription').value,
        bedrooms: document.getElementById('bedrooms').value,
        bathrooms: document.getElementById('bathrooms').value,
        yearBuilt: document.getElementById('yearBuilt').value,
        features: Array.from(document.querySelectorAll('input[name="features"]:checked')).map(cb => cb.value),
        contactName: document.getElementById('contactName').value,
        contactEmail: document.getElementById('contactEmail').value,
        contactPhone: document.getElementById('contactPhone').value,
        preferredContact: document.getElementById('preferredContact').value,
        images: uploadedImages.map(img => ({
            name: img.name,
            dataUrl: img.dataUrl
        }))
    };
    
    return data;
}

function loadDraft() {
    const draft = localStorage.getItem('propertyDraft');
    if (!draft) return;
    
    try {
        const data = JSON.parse(draft);
        
        // Populate form fields
        if (data.title) document.getElementById('propertyTitle').value = data.title;
        if (data.type) document.getElementById('propertyType').value = data.type;
        if (data.price) document.getElementById('propertyPrice').value = data.price;
        if (data.area) document.getElementById('propertyArea').value = data.area;
        if (data.location) document.getElementById('propertyLocation').value = data.location;
        if (data.description) document.getElementById('propertyDescription').value = data.description;
        if (data.bedrooms) document.getElementById('bedrooms').value = data.bedrooms;
        if (data.bathrooms) document.getElementById('bathrooms').value = data.bathrooms;
        if (data.yearBuilt) document.getElementById('yearBuilt').value = data.yearBuilt;
        if (data.contactName) document.getElementById('contactName').value = data.contactName;
        if (data.contactEmail) document.getElementById('contactEmail').value = data.contactEmail;
        if (data.contactPhone) document.getElementById('contactPhone').value = data.contactPhone;
        if (data.preferredContact) document.getElementById('preferredContact').value = data.preferredContact;
        
        // Populate features
        if (data.features) {
            data.features.forEach(feature => {
                const checkbox = document.querySelector(`input[name="features"][value="${feature}"]`);
                if (checkbox) checkbox.checked = true;
            });
        }
        
        // Show draft loaded message
        showToast('Draft Loaded', 'Your saved draft has been loaded.', 'info');
        
    } catch (error) {
        console.error('Error loading draft:', error);
    }
}

function clearDraft() {
    localStorage.removeItem('propertyDraft');
}

// Utility function for toast notifications
function showToast(title, message, type = 'info') {
    if (window.LuxEstate && window.LuxEstate.showToast) {
        window.LuxEstate.showToast(title, message, type);
    } else {
        alert(`${title}: ${message}`);
    }
}

// Check for draft on page load
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('loadDraft') === 'true') {
        setTimeout(loadDraft, 500);
    }
});

// Auto-save draft every 2 minutes
setInterval(() => {
    const form = document.getElementById('addPropertyForm');
    if (form && form.querySelector('input:not([value=""]), textarea:not([value=""]), select:not([value=""])')) {
        saveAsDraft();
    }
}, 120000); // 2 minutes

// Make functions globally available
window.AddProperty = {
    removeImage,
    submitProperty,
    saveAsDraft,
    loadDraft,
    clearDraft
};
