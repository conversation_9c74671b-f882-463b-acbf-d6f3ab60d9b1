/* ===== THEME SWITCHER STYLES ===== */

/* Light Theme (Default) */
[data-bs-theme="light"] {
    --bs-body-bg: #ffffff;
    --bs-body-color: #212529;
    --bs-emphasis-color: #000000;
    --bs-secondary-color: rgba(33, 37, 41, 0.75);
    --bs-tertiary-color: rgba(33, 37, 41, 0.5);
    --bs-border-color: #dee2e6;
    --bs-secondary-bg: #f8f9fa;
    --bs-tertiary-bg: #f1f3f4;
}

[data-bs-theme="light"] .property-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
}

[data-bs-theme="light"] .filter-sidebar {
    background: #ffffff;
    border: 1px solid #e9ecef;
}

[data-bs-theme="light"] .testimonial-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
}

/* Dark Theme */
[data-bs-theme="dark"] {
    --bs-body-bg: #1a1a1a;
    --bs-body-color: #ffffff;
    --bs-emphasis-color: #ffffff;
    --bs-secondary-color: rgba(255, 255, 255, 0.75);
    --bs-tertiary-color: rgba(255, 255, 255, 0.5);
    --bs-border-color: #495057;
    --bs-secondary-bg: #2d3748;
    --bs-tertiary-bg: #4a5568;
}

[data-bs-theme="dark"] .property-card {
    background: #2d3748;
    border: 1px solid #4a5568;
    color: #ffffff;
}

[data-bs-theme="dark"] .property-card .property-location {
    color: #a0aec0;
}

[data-bs-theme="dark"] .property-card .property-feature {
    color: #a0aec0;
}

[data-bs-theme="dark"] .filter-sidebar {
    background: #2d3748;
    border: 1px solid #4a5568;
    color: #ffffff;
}

[data-bs-theme="dark"] .testimonial-card {
    background: #2d3748;
    border: 1px solid #4a5568;
    color: #ffffff;
}

[data-bs-theme="dark"] .testimonial-text {
    color: #a0aec0;
}

[data-bs-theme="dark"] .testimonial-role {
    color: #a0aec0;
}

[data-bs-theme="dark"] .search-container {
    background: rgba(45, 55, 72, 0.95);
    border: 1px solid #4a5568;
}

[data-bs-theme="dark"] .search-container label {
    color: #ffffff !important;
}

[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .form-select {
    background: #4a5568;
    border-color: #718096;
    color: #ffffff;
}

[data-bs-theme="dark"] .form-control:focus,
[data-bs-theme="dark"] .form-select:focus {
    background: #4a5568;
    border-color: #ffc107;
    color: #ffffff;
}

[data-bs-theme="dark"] .form-control::placeholder {
    color: #a0aec0;
}

[data-bs-theme="dark"] .bg-light {
    background-color: #2d3748 !important;
}

[data-bs-theme="dark"] .text-muted {
    color: #a0aec0 !important;
}

/* Luxury Theme */
[data-bs-theme="luxury"] {
    --bs-body-bg: #0a0a0a;
    --bs-body-color: #d4af37;
    --bs-emphasis-color: #ffd700;
    --bs-secondary-color: rgba(212, 175, 55, 0.75);
    --bs-tertiary-color: rgba(212, 175, 55, 0.5);
    --bs-border-color: #2d2d2d;
    --bs-secondary-bg: #1a1a1a;
    --bs-tertiary-bg: #2d2d2d;
    --luxury-gold: #d4af37;
    --luxury-dark: #0a0a0a;
    --luxury-accent: #8b7355;
}

[data-bs-theme="luxury"] body {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
    background-attachment: fixed;
}

[data-bs-theme="luxury"] .navbar-dark {
    background: rgba(10, 10, 10, 0.95) !important;
    border-bottom: 1px solid #d4af37;
}

[data-bs-theme="luxury"] .property-card {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
    border: 1px solid #d4af37;
    color: #d4af37;
    box-shadow: 0 8px 32px rgba(212, 175, 55, 0.1);
}

[data-bs-theme="luxury"] .property-card:hover {
    box-shadow: 0 12px 40px rgba(212, 175, 55, 0.2);
    border-color: #ffd700;
}

[data-bs-theme="luxury"] .property-price {
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

[data-bs-theme="luxury"] .property-location,
[data-bs-theme="luxury"] .property-feature {
    color: #8b7355;
}

[data-bs-theme="luxury"] .filter-sidebar {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
    border: 1px solid #d4af37;
    color: #d4af37;
    box-shadow: 0 8px 32px rgba(212, 175, 55, 0.1);
}

[data-bs-theme="luxury"] .testimonial-card {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
    border: 1px solid #d4af37;
    color: #d4af37;
    box-shadow: 0 8px 32px rgba(212, 175, 55, 0.1);
}

[data-bs-theme="luxury"] .testimonial-text {
    color: #8b7355;
}

[data-bs-theme="luxury"] .testimonial-role {
    color: #8b7355;
}

[data-bs-theme="luxury"] .search-container {
    background: linear-gradient(145deg, rgba(26, 26, 26, 0.95), rgba(45, 45, 45, 0.95));
    border: 1px solid #d4af37;
    box-shadow: 0 8px 32px rgba(212, 175, 55, 0.2);
}

[data-bs-theme="luxury"] .search-container label {
    color: #d4af37 !important;
    font-weight: 600;
}

[data-bs-theme="luxury"] .form-control,
[data-bs-theme="luxury"] .form-select {
    background: #2d2d2d;
    border: 1px solid #d4af37;
    color: #d4af37;
}

[data-bs-theme="luxury"] .form-control:focus,
[data-bs-theme="luxury"] .form-select:focus {
    background: #2d2d2d;
    border-color: #ffd700;
    color: #d4af37;
    box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
}

[data-bs-theme="luxury"] .form-control::placeholder {
    color: #8b7355;
}

[data-bs-theme="luxury"] .btn-warning {
    background: linear-gradient(145deg, #d4af37, #ffd700);
    border: none;
    color: #0a0a0a;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

[data-bs-theme="luxury"] .btn-warning:hover {
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

[data-bs-theme="luxury"] .btn-outline-warning {
    border: 2px solid #d4af37;
    color: #d4af37;
    background: transparent;
}

[data-bs-theme="luxury"] .btn-outline-warning:hover {
    background: linear-gradient(145deg, #d4af37, #ffd700);
    border-color: #ffd700;
    color: #0a0a0a;
}

[data-bs-theme="luxury"] .bg-light {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d) !important;
}

[data-bs-theme="luxury"] .bg-dark {
    background: linear-gradient(145deg, #0a0a0a, #1a1a1a) !important;
}

[data-bs-theme="luxury"] .text-muted {
    color: #8b7355 !important;
}

[data-bs-theme="luxury"] .hero-overlay {
    background: linear-gradient(135deg, rgba(10, 10, 10, 0.7), rgba(212, 175, 55, 0.1));
}

[data-bs-theme="luxury"] .swiper-button-next,
[data-bs-theme="luxury"] .swiper-button-prev {
    background: linear-gradient(145deg, #d4af37, #ffd700);
    color: #0a0a0a;
    border: 1px solid #ffd700;
}

[data-bs-theme="luxury"] .swiper-button-next:hover,
[data-bs-theme="luxury"] .swiper-button-prev:hover {
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.4);
}

[data-bs-theme="luxury"] .swiper-pagination-bullet {
    background: #d4af37;
}

[data-bs-theme="luxury"] .swiper-pagination-bullet-active {
    background: #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

[data-bs-theme="luxury"] .chat-toggle {
    background: linear-gradient(145deg, #d4af37, #ffd700);
    color: #0a0a0a;
    box-shadow: 0 8px 32px rgba(212, 175, 55, 0.3);
}

[data-bs-theme="luxury"] .chat-toggle:hover {
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    box-shadow: 0 12px 40px rgba(212, 175, 55, 0.4);
}

[data-bs-theme="luxury"] .chat-window {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
    border: 1px solid #d4af37;
}

[data-bs-theme="luxury"] .chat-header {
    background: linear-gradient(145deg, #d4af37, #ffd700);
    color: #0a0a0a;
}

[data-bs-theme="luxury"] .chat-body {
    color: #d4af37;
}

[data-bs-theme="luxury"] .quick-actions .btn {
    border-color: #d4af37;
    color: #d4af37;
    background: transparent;
}

[data-bs-theme="luxury"] .quick-actions .btn:hover {
    background: linear-gradient(145deg, #d4af37, #ffd700);
    color: #0a0a0a;
    border-color: #ffd700;
}

/* Theme Transition */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Theme Switcher Button Styles */
.theme-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.theme-option:hover {
    background-color: var(--bs-secondary-bg);
    color: var(--luxury-gold);
}

.theme-option i {
    width: 16px;
}

/* Luxury Theme Animations */
[data-bs-theme="luxury"] .property-card,
[data-bs-theme="luxury"] .testimonial-card,
[data-bs-theme="luxury"] .filter-sidebar {
    position: relative;
    overflow: hidden;
}

[data-bs-theme="luxury"] .property-card::before,
[data-bs-theme="luxury"] .testimonial-card::before,
[data-bs-theme="luxury"] .filter-sidebar::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(212, 175, 55, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

[data-bs-theme="luxury"] .property-card:hover::before,
[data-bs-theme="luxury"] .testimonial-card:hover::before,
[data-bs-theme="luxury"] .filter-sidebar:hover::before {
    opacity: 1;
    animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

/* Responsive Theme Adjustments */
@media (max-width: 768px) {
    [data-bs-theme="luxury"] .search-container {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    [data-bs-theme="luxury"] .property-card {
        margin-bottom: 1.5rem;
    }
}

/* Print Styles */
@media print {
    [data-bs-theme="luxury"],
    [data-bs-theme="dark"] {
        --bs-body-bg: #ffffff !important;
        --bs-body-color: #000000 !important;
    }
    
    .floating-chat,
    .navbar,
    .btn {
        display: none !important;
    }
}
