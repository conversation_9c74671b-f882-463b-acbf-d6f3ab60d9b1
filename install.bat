@echo off
REM LuxEstate Django Backend Installation Script for Windows
REM This script sets up the complete Django backend with virtual environment

echo.
echo 🏠 LuxEstate - Luxury Real Estate Backend Installation
echo ==================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

REM Get Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [INFO] Python %PYTHON_VERSION% detected

REM Create virtual environment
echo [INFO] Creating virtual environment...
set VENV_NAME=luxestate_env

if exist %VENV_NAME% (
    echo [WARNING] Virtual environment '%VENV_NAME%' already exists
    set /p RECREATE="Do you want to recreate it? (y/N): "
    if /i "%RECREATE%"=="y" (
        rmdir /s /q %VENV_NAME%
        python -m venv %VENV_NAME%
        echo [SUCCESS] Virtual environment recreated
    ) else (
        echo [INFO] Using existing virtual environment
    )
) else (
    python -m venv %VENV_NAME%
    echo [SUCCESS] Virtual environment created
)

REM Activate virtual environment
echo [INFO] Activating virtual environment...
call %VENV_NAME%\Scripts\activate.bat
echo [SUCCESS] Virtual environment activated

REM Upgrade pip
echo [INFO] Upgrading pip...
python -m pip install --upgrade pip

REM Install dependencies
echo [INFO] Installing Python dependencies...
if exist requirements.txt (
    pip install -r requirements.txt
    echo [SUCCESS] Dependencies installed successfully
) else (
    echo [ERROR] requirements.txt not found
    pause
    exit /b 1
)

REM Create .env file
echo [INFO] Setting up environment configuration...
if not exist .env (
    echo [INFO] Creating .env file...
    (
        echo # Django Settings
        echo SECRET_KEY=django-insecure-your-secret-key-here-change-in-production
        echo DEBUG=True
        echo ALLOWED_HOSTS=localhost,127.0.0.1
        echo.
        echo # Database ^(SQLite for development^)
        echo DB_ENGINE=django.db.backends.sqlite3
        echo DB_NAME=db.sqlite3
        echo.
        echo # Email Settings ^(Console backend for development^)
        echo EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
        echo.
        echo # Celery Settings
        echo CELERY_BROKER_URL=redis://localhost:6379/0
        echo CELERY_RESULT_BACKEND=redis://localhost:6379/0
        echo.
        echo # Stripe Settings ^(Add your keys^)
        echo STRIPE_PUBLISHABLE_KEY=pk_test_your_key_here
        echo STRIPE_SECRET_KEY=sk_test_your_key_here
    ) > .env
    echo [SUCCESS] .env file created
) else (
    echo [WARNING] .env file already exists
)

REM Setup Django project
echo [INFO] Setting up Django project...

REM Run migrations
echo [INFO] Running database migrations...
python manage.py makemigrations
python manage.py migrate
echo [SUCCESS] Database migrations completed

REM Collect static files
echo [INFO] Collecting static files...
python manage.py collectstatic --noinput
echo [SUCCESS] Static files collected

REM Setup frontend files
echo [INFO] Setting up frontend files...

REM Create templates directory
if not exist templates mkdir templates

REM Copy HTML files to templates
if exist index.html (
    copy *.html templates\ >nul 2>&1
    echo [SUCCESS] HTML files copied to templates/
)

REM Create static directory structure
if not exist static\assets\css mkdir static\assets\css
if not exist static\assets\js mkdir static\assets\js
if not exist static\assets\images mkdir static\assets\images

REM Copy assets if they exist
if exist assets (
    xcopy assets\* static\assets\ /E /I /Q >nul 2>&1
    echo [SUCCESS] Assets copied to static/
)

REM Create superuser
echo [INFO] Creating superuser account...
echo Please create a superuser account for Django admin:
python manage.py createsuperuser

echo.
echo [SUCCESS] 🎉 Installation completed successfully!
echo.
echo 📋 Next steps:
echo 1. Activate virtual environment: %VENV_NAME%\Scripts\activate.bat
echo 2. Start development server: python manage.py runserver
echo 3. Visit: http://127.0.0.1:8000/
echo 4. Admin panel: http://127.0.0.1:8000/admin/
echo 5. API documentation: http://127.0.0.1:8000/api/
echo.
echo 🔧 Optional services:
echo - Install Redis for Windows from: https://github.com/microsoftarchive/redis/releases
echo - Start Celery worker: celery -A luxestate_backend worker -l info
echo - Start Celery beat: celery -A luxestate_backend beat -l info
echo.
echo [SUCCESS] Happy coding! 🚀
echo.
pause
