# -*- coding: utf-8 -*-
from allauth.socialaccount.providers.netiq.provider import NetIQ<PERSON>rovider
from allauth.socialaccount.tests import OAuth2TestsMixin
from allauth.tests import MockedResponse, TestCase


class NetIQTests(OAuth2TestsMixin, TestCase):
    provider_id = NetIQProvider.id

    def get_mocked_response(self):
        return MockedResponse(
            200,
            """
            {
                "sub": "d4c094dd899ab0408fb9d4c094dd899a",
                "acr": "secure/name/password/uri",
                "preferred_username": "Mocktest",
                "email": "<EMAIL>",
                "nickname": "<PERSON>ck<PERSON>",
                "family_name": "test",
                "given_name": "Mock",
                "website": "https://www.exanple.com"
            }
        """,
        )
