# LuxEstate - Luxury Real Estate Website

A modern, responsive luxury real estate website built with Bootstrap 5, featuring advanced theme switching, property management, and interactive user experiences.

## 🌟 Features

### Core Functionality
- **Responsive Design**: Mobile-first approach with Bootstrap 5
- **Theme Switching**: Light, Dark, and Luxury themes with smooth transitions
- **Property Listings**: Advanced filtering, sorting, and search capabilities
- **Property Details**: Detailed property pages with image galleries and maps
- **User Authentication**: Login/Register system with form validation
- **Property Management**: Add, edit, and manage property listings
- **Favorites System**: Save and manage favorite properties
- **Dashboard**: User dashboard with analytics and property management

### Interactive Features
- **Floating Chat Widget**: Contextual help and support
- **Advanced Search**: Multi-criteria property search with real-time filtering
- **Image Galleries**: Swiper.js powered image carousels
- **Maps Integration**: Leaflet maps for property locations
- **Form Validation**: Client-side validation with Bootstrap
- **Animations**: AOS (Animate On Scroll) library integration
- **Toast Notifications**: User feedback system

### Pages Included
- **Homepage**: Hero section, featured properties, testimonials, stats
- **Property Listings**: Filterable property grid with pagination
- **Property Details**: Comprehensive property information with galleries
- **Add Property**: Multi-step property submission form
- **User Dashboard**: Property management and analytics
- **Authentication**: Login and registration pages
- **About Us**: Company information and team
- **Contact**: Contact form with office locations and map
- **Blog**: News and insights section
- **Favorites**: Saved properties management

## 🛠️ Technologies Used

### Frontend
- **Bootstrap 5.3.2**: CSS framework for responsive design
- **Bootstrap Icons**: Icon library
- **Swiper.js**: Touch slider for image galleries
- **AOS**: Animate On Scroll library
- **Leaflet**: Interactive maps

### JavaScript Features
- **Theme Management**: Advanced theme switching system
- **Property Data Management**: Local storage and dynamic content
- **Form Handling**: Validation and submission
- **Interactive Components**: Modals, dropdowns, carousels
- **Responsive Navigation**: Mobile-friendly navigation

## 📁 Project Structure

```
luxestate/
├── index.html              # Homepage
├── listings.html           # Property listings page
├── property-detail.html    # Property detail page
├── add-property.html       # Add property form
├── dashboard.html          # User dashboard
├── login.html             # Authentication page
├── about.html             # About us page
├── contact.html           # Contact page
├── blog.html              # Blog page
├── favorites.html         # Favorites page
├── assets/
│   ├── css/
│   │   ├── style.css      # Main stylesheet
│   │   └── themes.css     # Theme-specific styles
│   └── js/
│       ├── main.js        # Core functionality
│       ├── theme-switcher.js  # Theme management
│       ├── property-data.js   # Property data handling
│       ├── listings.js    # Listings page functionality
│       ├── property-detail.js # Property detail functionality
│       ├── add-property.js    # Add property functionality
│       └── dashboard.js   # Dashboard functionality
└── README.md
```

## 🚀 Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Local web server (optional, for development)

### Installation
1. Clone or download the project files
2. Open `index.html` in your web browser
3. For development, use a local server:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```

### Usage
1. **Browse Properties**: Visit the listings page to explore available properties
2. **View Details**: Click on any property to see detailed information
3. **Add to Favorites**: Save properties you're interested in
4. **Theme Switching**: Use the theme switcher in the navigation
5. **Add Properties**: Use the add property form to list new properties
6. **User Dashboard**: Access your dashboard after login simulation

## 🎨 Theme System

The website features three distinct themes:

### Light Theme (Default)
- Clean, bright interface
- Professional appearance
- High contrast for readability

### Dark Theme
- Modern dark interface
- Reduced eye strain
- Elegant color scheme

### Luxury Theme
- Premium gold accents
- Sophisticated gradients
- Exclusive luxury feel

## 📱 Responsive Design

- **Mobile First**: Optimized for mobile devices
- **Tablet Friendly**: Adapted layouts for tablets
- **Desktop Enhanced**: Full-featured desktop experience
- **Touch Optimized**: Touch-friendly interactions

## 🔧 Customization

### Colors
Modify CSS custom properties in `assets/css/style.css`:
```css
:root {
    --luxury-gold: #ffc107;
    --luxury-dark: #1a1a1a;
    --luxury-light: #f8f9fa;
    /* Add your custom colors */
}
```

### Themes
Add new themes in `assets/css/themes.css`:
```css
[data-bs-theme="your-theme"] {
    /* Your theme styles */
}
```

### Content
- Update property data in `assets/js/property-data.js`
- Modify testimonials and sample content
- Replace placeholder images with actual property photos

## 🌐 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

1. Fork the project
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Open a Pull Request

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Phone: +****************

## 🔮 Future Enhancements

- Backend integration with REST API
- Real-time chat functionality
- Advanced property comparison
- Virtual tour integration
- Payment gateway integration
- Multi-language support
- Progressive Web App (PWA) features

---

**LuxEstate** - Redefining luxury real estate experiences through innovative web technology.
