"""
Payment models for the LuxEstate application.
"""

from django.db import models
from django.contrib.auth import get_user_model
from decimal import Decimal

User = get_user_model()

class PaymentMethod(models.Model):
    """User payment methods."""
    
    PAYMENT_TYPES = (
        ('card', 'Credit/Debit Card'),
        ('bank', 'Bank Account'),
        ('paypal', 'PayPal'),
        ('stripe', 'Stripe'),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payment_methods')
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPES)
    
    # Card details (encrypted)
    card_last_four = models.CharField(max_length=4, blank=True)
    card_brand = models.CharField(max_length=20, blank=True)
    card_exp_month = models.PositiveIntegerField(null=True, blank=True)
    card_exp_year = models.PositiveIntegerField(null=True, blank=True)
    
    # External payment provider IDs
    stripe_payment_method_id = models.CharField(max_length=100, blank=True)
    paypal_payment_method_id = models.CharField(max_length=100, blank=True)
    
    # Settings
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'payment_methods'
        verbose_name = 'Payment Method'
        verbose_name_plural = 'Payment Methods'
    
    def __str__(self):
        if self.payment_type == 'card' and self.card_last_four:
            return f"{self.card_brand} ending in {self.card_last_four}"
        return f"{self.get_payment_type_display()}"

class Transaction(models.Model):
    """Financial transactions."""
    
    TRANSACTION_TYPES = (
        ('listing_fee', 'Listing Fee'),
        ('premium_listing', 'Premium Listing'),
        ('featured_listing', 'Featured Listing'),
        ('commission', 'Commission'),
        ('subscription', 'Subscription'),
        ('refund', 'Refund'),
    )
    
    TRANSACTION_STATUS = (
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
    )
    
    # Basic information
    transaction_id = models.CharField(max_length=100, unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='transactions')
    transaction_type = models.CharField(max_length=30, choices=TRANSACTION_TYPES)
    status = models.CharField(max_length=20, choices=TRANSACTION_STATUS, default='pending')
    
    # Amount details
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD')
    fee_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    net_amount = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Related objects
    property = models.ForeignKey('properties.Property', on_delete=models.SET_NULL, null=True, blank=True)
    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Payment provider details
    stripe_payment_intent_id = models.CharField(max_length=100, blank=True)
    stripe_charge_id = models.CharField(max_length=100, blank=True)
    paypal_transaction_id = models.CharField(max_length=100, blank=True)
    
    # Additional data
    description = models.TextField(blank=True)
    metadata = models.JSONField(default=dict)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'transactions'
        ordering = ['-created_at']
        verbose_name = 'Transaction'
        verbose_name_plural = 'Transactions'
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['transaction_type', 'status']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.transaction_id} - {self.user.get_full_name()} - ${self.amount}"
    
    def save(self, *args, **kwargs):
        # Calculate net amount
        self.net_amount = self.amount - self.fee_amount
        super().save(*args, **kwargs)

class Subscription(models.Model):
    """User subscriptions for premium features."""
    
    SUBSCRIPTION_TYPES = (
        ('basic', 'Basic'),
        ('premium', 'Premium'),
        ('professional', 'Professional'),
        ('enterprise', 'Enterprise'),
    )
    
    SUBSCRIPTION_STATUS = (
        ('active', 'Active'),
        ('cancelled', 'Cancelled'),
        ('expired', 'Expired'),
        ('suspended', 'Suspended'),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='subscriptions')
    subscription_type = models.CharField(max_length=20, choices=SUBSCRIPTION_TYPES)
    status = models.CharField(max_length=20, choices=SUBSCRIPTION_STATUS, default='active')
    
    # Pricing
    monthly_price = models.DecimalField(max_digits=8, decimal_places=2)
    annual_price = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    is_annual = models.BooleanField(default=False)
    
    # Billing
    stripe_subscription_id = models.CharField(max_length=100, blank=True)
    current_period_start = models.DateTimeField()
    current_period_end = models.DateTimeField()
    
    # Features
    max_listings = models.PositiveIntegerField(default=5)
    max_featured_listings = models.PositiveIntegerField(default=1)
    analytics_access = models.BooleanField(default=False)
    priority_support = models.BooleanField(default=False)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    cancelled_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'subscriptions'
        ordering = ['-created_at']
        verbose_name = 'Subscription'
        verbose_name_plural = 'Subscriptions'
    
    def __str__(self):
        return f"{self.user.get_full_name()} - {self.get_subscription_type_display()}"
    
    @property
    def is_active(self):
        """Check if subscription is currently active."""
        return self.status == 'active' and self.current_period_end > timezone.now()
    
    @property
    def days_remaining(self):
        """Get days remaining in current period."""
        if self.current_period_end:
            delta = self.current_period_end - timezone.now()
            return max(0, delta.days)
        return 0

class Invoice(models.Model):
    """Invoices for transactions."""
    
    INVOICE_STATUS = (
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
        ('cancelled', 'Cancelled'),
    )
    
    # Basic information
    invoice_number = models.CharField(max_length=50, unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='invoices')
    transaction = models.OneToOneField(Transaction, on_delete=models.CASCADE, related_name='invoice')
    
    # Invoice details
    status = models.CharField(max_length=20, choices=INVOICE_STATUS, default='draft')
    subtotal = models.DecimalField(max_digits=10, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Dates
    issue_date = models.DateField()
    due_date = models.DateField()
    paid_date = models.DateField(null=True, blank=True)
    
    # Additional information
    notes = models.TextField(blank=True)
    terms = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'invoices'
        ordering = ['-created_at']
        verbose_name = 'Invoice'
        verbose_name_plural = 'Invoices'
    
    def __str__(self):
        return f"Invoice {self.invoice_number} - {self.user.get_full_name()}"
    
    def save(self, *args, **kwargs):
        # Calculate total amount
        self.total_amount = self.subtotal + self.tax_amount
        super().save(*args, **kwargs)

class Commission(models.Model):
    """Commission tracking for agents."""
    
    COMMISSION_STATUS = (
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('paid', 'Paid'),
        ('disputed', 'Disputed'),
    )
    
    agent = models.ForeignKey(User, on_delete=models.CASCADE, related_name='commissions')
    property = models.ForeignKey('properties.Property', on_delete=models.CASCADE, related_name='commissions')
    transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, related_name='commissions')
    
    # Commission details
    sale_price = models.DecimalField(max_digits=15, decimal_places=2)
    commission_rate = models.DecimalField(max_digits=5, decimal_places=4)  # e.g., 0.0600 for 6%
    commission_amount = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Status and dates
    status = models.CharField(max_length=20, choices=COMMISSION_STATUS, default='pending')
    earned_date = models.DateField()
    paid_date = models.DateField(null=True, blank=True)
    
    # Additional information
    notes = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'commissions'
        ordering = ['-created_at']
        verbose_name = 'Commission'
        verbose_name_plural = 'Commissions'
    
    def __str__(self):
        return f"{self.agent.get_full_name()} - {self.property.title} - ${self.commission_amount}"
    
    def save(self, *args, **kwargs):
        # Calculate commission amount
        self.commission_amount = self.sale_price * self.commission_rate
        super().save(*args, **kwargs)
