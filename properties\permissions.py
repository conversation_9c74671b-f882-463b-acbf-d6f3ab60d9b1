"""
Permissions for the properties app.
"""

from rest_framework import permissions

class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions are only allowed to the owner of the property.
        return obj.owner == request.user or request.user.is_staff

class IsAgentOrOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to allow agents and owners to edit properties.
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions are allowed to owner, agent, or staff
        return (
            obj.owner == request.user or 
            obj.agent == request.user or 
            request.user.is_staff
        )
