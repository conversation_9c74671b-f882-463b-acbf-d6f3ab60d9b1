"""
Admin configuration for properties app.
"""

from django.contrib import admin
from django.utils.html import format_html
from .models import PropertyCategory, Property, PropertyImage, PropertyView

@admin.register(PropertyCategory)
class PropertyCategoryAdmin(admin.ModelAdmin):
    """Admin for PropertyCategory model."""
    
    list_display = ['name', 'parent', 'is_active']
    list_filter = ['is_active', 'parent']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    ordering = ['name']

class PropertyImageInline(admin.TabularInline):
    """Inline admin for PropertyImage."""
    
    model = PropertyImage
    extra = 1
    fields = ['image', 'caption', 'alt_text', 'order', 'is_featured']
    readonly_fields = ['thumbnail_preview']
    
    def thumbnail_preview(self, obj):
        """Show thumbnail preview."""
        if obj.thumbnail:
            return format_html('<img src="{}" width="100" height="67" />', obj.thumbnail.url)
        return "No thumbnail"
    thumbnail_preview.short_description = "Thumbnail"

@admin.register(Property)
class PropertyAdmin(admin.ModelAdmin):
    """Admin for Property model."""
    
    list_display = [
        'title', 'property_type', 'listing_type', 'status', 'price',
        'city', 'state', 'owner', 'is_featured', 'views_count', 'created_at'
    ]
    list_filter = [
        'property_type', 'listing_type', 'status', 'is_featured',
        'is_premium', 'city', 'state', 'created_at'
    ]
    search_fields = ['title', 'description', 'address', 'city', 'state']
    prepopulated_fields = {'slug': ('title',)}
    readonly_fields = [
        'views_count', 'inquiries_count', 'favorites_count',
        'price_per_sqft', 'created_at', 'updated_at'
    ]
    
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'title', 'slug', 'description', 'category',
                'property_type', 'listing_type', 'status'
            )
        }),
        ('Pricing', {
            'fields': ('price', 'rent_price', 'price_per_sqft')
        }),
        ('Location', {
            'fields': (
                'address', 'city', 'state', 'country', 'zip_code',
                'latitude', 'longitude'
            )
        }),
        ('Property Details', {
            'fields': (
                'bedrooms', 'bathrooms', 'area', 'lot_size',
                'year_built', 'floors', 'parking_spaces'
            )
        }),
        ('Features & Amenities', {
            'fields': ('features', 'amenities'),
            'classes': ('collapse',)
        }),
        ('Media', {
            'fields': ('featured_image', 'virtual_tour_url', 'video_url')
        }),
        ('SEO & Marketing', {
            'fields': (
                'meta_title', 'meta_description', 'tags',
                'is_featured', 'is_premium'
            ),
            'classes': ('collapse',)
        }),
        ('Management', {
            'fields': ('owner', 'agent')
        }),
        ('Statistics', {
            'fields': (
                'views_count', 'inquiries_count', 'favorites_count'
            ),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'published_at', 'expires_at'),
            'classes': ('collapse',)
        })
    )
    
    inlines = [PropertyImageInline]
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related(
            'owner', 'agent', 'category'
        )

@admin.register(PropertyImage)
class PropertyImageAdmin(admin.ModelAdmin):
    """Admin for PropertyImage model."""
    
    list_display = ['property', 'caption', 'order', 'is_featured', 'created_at']
    list_filter = ['is_featured', 'created_at']
    search_fields = ['property__title', 'caption', 'alt_text']
    ordering = ['property', 'order']
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('property')

@admin.register(PropertyView)
class PropertyViewAdmin(admin.ModelAdmin):
    """Admin for PropertyView model."""
    
    list_display = ['property', 'user', 'ip_address', 'viewed_at']
    list_filter = ['viewed_at']
    search_fields = ['property__title', 'user__username', 'ip_address']
    readonly_fields = ['property', 'user', 'ip_address', 'user_agent', 'referrer', 'viewed_at']
    
    def has_add_permission(self, request):
        """Disable add permission."""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Disable change permission."""
        return False
    
    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('property', 'user')
