"""
Serializers for the properties app.
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.db import models
from .models import Property, PropertyCategory, PropertyImage, PropertyView

User = get_user_model()

class PropertyCategorySerializer(serializers.ModelSerializer):
    """Serializer for PropertyCategory model."""
    
    children = serializers.SerializerMethodField()
    
    class Meta:
        model = PropertyCategory
        fields = ['id', 'name', 'slug', 'description', 'icon', 'is_active', 'children']
    
    def get_children(self, obj):
        """Get child categories."""
        if obj.children.exists():
            return PropertyCategorySerializer(obj.children.filter(is_active=True), many=True).data
        return []

class PropertyImageSerializer(serializers.ModelSerializer):
    """Serializer for PropertyImage model."""
    
    class Meta:
        model = PropertyImage
        fields = ['id', 'image', 'thumbnail', 'caption', 'alt_text', 'order', 'is_featured']

class PropertyOwnerSerializer(serializers.ModelSerializer):
    """Serializer for property owner/agent information."""
    
    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'email', 'phone_number', 'avatar']

class PropertyListSerializer(serializers.ModelSerializer):
    """Serializer for property list view."""
    
    location = serializers.ReadOnlyField()
    featured_image_url = serializers.SerializerMethodField()
    owner_name = serializers.SerializerMethodField()
    agent_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Property
        fields = [
            'id', 'title', 'slug', 'property_type', 'listing_type', 'status',
            'price', 'rent_price', 'location', 'city', 'state',
            'bedrooms', 'bathrooms', 'area', 'featured_image_url',
            'is_featured', 'is_premium', 'views_count', 'favorites_count',
            'owner_name', 'agent_name', 'created_at', 'updated_at'
        ]
    
    def get_featured_image_url(self, obj):
        """Get featured image URL."""
        if obj.featured_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.featured_image.url)
            return obj.featured_image.url
        return None
    
    def get_owner_name(self, obj):
        """Get owner full name."""
        return obj.owner.get_full_name()
    
    def get_agent_name(self, obj):
        """Get agent full name."""
        return obj.agent.get_full_name() if obj.agent else None

class PropertyDetailSerializer(serializers.ModelSerializer):
    """Serializer for property detail view."""
    
    category = PropertyCategorySerializer(read_only=True)
    images = PropertyImageSerializer(many=True, read_only=True)
    owner = PropertyOwnerSerializer(read_only=True)
    agent = PropertyOwnerSerializer(read_only=True)
    location = serializers.ReadOnlyField()
    full_address = serializers.ReadOnlyField()
    tags_list = serializers.SerializerMethodField()
    
    class Meta:
        model = Property
        fields = [
            'id', 'title', 'slug', 'description', 'category', 'property_type',
            'listing_type', 'status', 'price', 'rent_price', 'price_per_sqft',
            'address', 'city', 'state', 'country', 'zip_code', 'location',
            'full_address', 'latitude', 'longitude', 'bedrooms', 'bathrooms',
            'area', 'lot_size', 'year_built', 'floors', 'parking_spaces',
            'features', 'amenities', 'featured_image', 'virtual_tour_url',
            'video_url', 'meta_title', 'meta_description', 'tags_list',
            'is_featured', 'is_premium', 'images', 'owner', 'agent',
            'views_count', 'inquiries_count', 'favorites_count',
            'created_at', 'updated_at', 'published_at'
        ]
    
    def get_tags_list(self, obj):
        """Get tags as list of strings."""
        return [tag.name for tag in obj.tags.all()]

class PropertyCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating/updating properties."""
    
    images = PropertyImageSerializer(many=True, read_only=True)
    uploaded_images = serializers.ListField(
        child=serializers.ImageField(),
        write_only=True,
        required=False
    )
    
    class Meta:
        model = Property
        fields = [
            'title', 'description', 'category', 'property_type', 'listing_type',
            'status', 'price', 'rent_price', 'address', 'city', 'state',
            'country', 'zip_code', 'latitude', 'longitude', 'bedrooms',
            'bathrooms', 'area', 'lot_size', 'year_built', 'floors',
            'parking_spaces', 'features', 'amenities', 'featured_image',
            'virtual_tour_url', 'video_url', 'meta_title', 'meta_description',
            'is_featured', 'is_premium', 'agent', 'images', 'uploaded_images'
        ]
    
    def create(self, validated_data):
        """Create property with images."""
        uploaded_images = validated_data.pop('uploaded_images', [])
        property_instance = Property.objects.create(**validated_data)
        
        # Create property images
        for index, image in enumerate(uploaded_images):
            PropertyImage.objects.create(
                property=property_instance,
                image=image,
                order=index
            )
        
        return property_instance
    
    def update(self, instance, validated_data):
        """Update property with images."""
        uploaded_images = validated_data.pop('uploaded_images', [])
        
        # Update property fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Add new images
        if uploaded_images:
            current_max_order = instance.images.aggregate(
                max_order=models.Max('order')
            )['max_order'] or 0
            
            for index, image in enumerate(uploaded_images):
                PropertyImage.objects.create(
                    property=instance,
                    image=image,
                    order=current_max_order + index + 1
                )
        
        return instance

class PropertySearchSerializer(serializers.Serializer):
    """Serializer for property search parameters."""
    
    query = serializers.CharField(required=False, allow_blank=True)
    listing_type = serializers.ChoiceField(
        choices=Property.LISTING_TYPES,
        required=False,
        allow_blank=True
    )
    property_type = serializers.ChoiceField(
        choices=Property.PROPERTY_TYPES,
        required=False,
        allow_blank=True
    )
    min_price = serializers.DecimalField(max_digits=15, decimal_places=2, required=False)
    max_price = serializers.DecimalField(max_digits=15, decimal_places=2, required=False)
    min_bedrooms = serializers.IntegerField(required=False)
    max_bedrooms = serializers.IntegerField(required=False)
    min_bathrooms = serializers.DecimalField(max_digits=3, decimal_places=1, required=False)
    max_bathrooms = serializers.DecimalField(max_digits=3, decimal_places=1, required=False)
    min_area = serializers.IntegerField(required=False)
    max_area = serializers.IntegerField(required=False)
    city = serializers.CharField(required=False, allow_blank=True)
    state = serializers.CharField(required=False, allow_blank=True)
    features = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        allow_empty=True
    )
    amenities = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        allow_empty=True
    )
    is_featured = serializers.BooleanField(required=False)
    is_premium = serializers.BooleanField(required=False)
    
    # Sorting options
    ordering = serializers.ChoiceField(
        choices=[
            ('created_at', 'Newest First'),
            ('-created_at', 'Oldest First'),
            ('price', 'Price: Low to High'),
            ('-price', 'Price: High to Low'),
            ('area', 'Area: Small to Large'),
            ('-area', 'Area: Large to Small'),
            ('-views_count', 'Most Viewed'),
            ('-favorites_count', 'Most Favorited'),
        ],
        required=False,
        default='-created_at'
    )

class PropertyViewSerializer(serializers.ModelSerializer):
    """Serializer for property views."""
    
    class Meta:
        model = PropertyView
        fields = ['id', 'property', 'user', 'ip_address', 'user_agent', 'referrer', 'viewed_at']
        read_only_fields = ['id', 'viewed_at']
