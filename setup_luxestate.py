#!/usr/bin/env python3
"""
LuxEstate Complete Setup Script (No Payments)
This script sets up the complete luxury real estate platform without payment functionality.
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path

class Colors:
    """ANSI color codes for terminal output."""
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

def print_header():
    """Print the application header."""
    print(f"{Colors.CYAN}")
    print("=" * 70)
    print("🏠 LuxEstate - Luxury Real Estate Platform Setup")
    print("=" * 70)
    print(f"{Colors.NC}")

def print_status(message):
    """Print status message."""
    print(f"{Colors.BLUE}[INFO]{Colors.NC} {message}")

def print_success(message):
    """Print success message."""
    print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {message}")

def print_warning(message):
    """Print warning message."""
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")

def print_error(message):
    """Print error message."""
    print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")

def run_command(command, description, cwd=None):
    """Run a command and handle errors."""
    print_status(f"{description}...")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True,
            cwd=cwd
        )
        print_success(f"{description} completed!")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print_error(f"Failed during {description}")
        print(f"Command: {command}")
        print(f"Error: {e.stderr}")
        return None

def check_system_requirements():
    """Check system requirements."""
    print_status("Checking system requirements...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print_error("Python 3.8 or higher is required!")
        print(f"Current version: {python_version.major}.{python_version.minor}")
        sys.exit(1)
    
    print_success(f"Python {python_version.major}.{python_version.minor} detected")
    
    # Check if we're in the right directory
    required_files = ['requirements.txt', 'manage.py', 'luxestate_backend']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print_error(f"Missing required files: {', '.join(missing_files)}")
        print("Please run this script from the project root directory.")
        sys.exit(1)
    
    print_success("All required files found")
    return True

def setup_virtual_environment():
    """Set up Python virtual environment."""
    print_status("Setting up virtual environment...")
    
    venv_name = "luxestate_env"
    
    # Check if virtual environment exists
    if os.path.exists(venv_name):
        print_warning(f"Virtual environment '{venv_name}' already exists")
        response = input(f"{Colors.YELLOW}Do you want to recreate it? (y/N): {Colors.NC}").strip().lower()
        if response == 'y':
            print_status("Removing existing virtual environment...")
            shutil.rmtree(venv_name)
        else:
            print_status("Using existing virtual environment")
    
    # Create virtual environment if it doesn't exist
    if not os.path.exists(venv_name):
        run_command(f"python -m venv {venv_name}", "Creating virtual environment")
    
    # Determine commands based on OS
    if platform.system() == "Windows":
        activate_cmd = f"{venv_name}\\Scripts\\activate.bat"
        pip_cmd = f"{venv_name}\\Scripts\\pip"
        python_cmd = f"{venv_name}\\Scripts\\python"
    else:
        activate_cmd = f"source {venv_name}/bin/activate"
        pip_cmd = f"{venv_name}/bin/pip"
        python_cmd = f"{venv_name}/bin/python"
    
    # Upgrade pip and install requirements
    run_command(f"{pip_cmd} install --upgrade pip", "Upgrading pip")
    run_command(f"{pip_cmd} install -r requirements.txt", "Installing Python dependencies")
    
    return python_cmd, activate_cmd, pip_cmd

def create_environment_file():
    """Create .env file with configuration."""
    print_status("Creating environment configuration...")
    
    if os.path.exists('.env'):
        print_warning(".env file already exists")
        response = input(f"{Colors.YELLOW}Do you want to overwrite it? (y/N): {Colors.NC}").strip().lower()
        if response != 'y':
            print_status("Keeping existing .env file")
            return
    
    env_content = """# Django Settings
SECRET_KEY=django-insecure-luxestate-development-key-change-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (SQLite for development)
DB_ENGINE=django.db.backends.sqlite3
DB_NAME=db.sqlite3

# Email Settings (Console backend for development)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# Celery Settings (Optional - for background tasks)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Application Settings
PROPERTY_IMAGE_MAX_SIZE=5242880
PROPERTY_MAX_IMAGES=20
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print_success(".env file created")

def setup_django_project(python_cmd):
    """Set up Django project."""
    print_status("Setting up Django project...")
    
    # Create necessary directories
    directories = [
        'static/assets/css',
        'static/assets/js', 
        'static/assets/images',
        'media/properties',
        'media/avatars',
        'templates',
        'logs'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print_success("Project directories created")
    
    # Run Django management commands
    run_command(f"{python_cmd} manage.py makemigrations", "Creating database migrations")
    run_command(f"{python_cmd} manage.py migrate", "Running database migrations")
    
    # Copy frontend files if they exist
    setup_frontend_integration()
    
    # Collect static files
    run_command(f"{python_cmd} manage.py collectstatic --noinput", "Collecting static files")

def setup_frontend_integration():
    """Integrate frontend files with Django."""
    print_status("Setting up frontend integration...")
    
    # List of HTML files to copy
    html_files = [
        'index.html', 'listings.html', 'property-detail.html',
        'dashboard.html', 'favorites.html', 'add-property.html',
        'about.html', 'contact.html', 'blog.html', 'blog-detail.html'
    ]
    
    # Copy HTML files to templates directory
    copied_files = []
    for html_file in html_files:
        if os.path.exists(html_file):
            shutil.copy2(html_file, 'templates/')
            copied_files.append(html_file)
    
    if copied_files:
        print_success(f"Copied {len(copied_files)} HTML files to templates/")
    
    # Copy assets directory if it exists
    if os.path.exists('assets'):
        if os.path.exists('static/assets'):
            shutil.rmtree('static/assets')
        shutil.copytree('assets', 'static/assets')
        print_success("Copied assets to static/assets/")
    else:
        print_warning("Assets directory not found - frontend files may not be available")

def create_sample_data(python_cmd):
    """Create sample data for development."""
    print_status("Creating sample data...")
    
    sample_data_script = '''
import os
import django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "luxestate_backend.settings")
django.setup()

from django.contrib.auth import get_user_model
from properties.models import Property, PropertyCategory
from decimal import Decimal

User = get_user_model()

# Create superuser
print("Creating admin user...")
if not User.objects.filter(username="admin").exists():
    admin_user = User.objects.create_superuser(
        username="admin",
        email="<EMAIL>",
        password="admin123",
        first_name="Admin",
        last_name="User",
        user_type="admin"
    )
    print("✅ Admin user created (username: admin, password: admin123)")
else:
    print("ℹ️ Admin user already exists")

# Create agent user
print("Creating agent user...")
if not User.objects.filter(username="agent").exists():
    agent_user = User.objects.create_user(
        username="agent",
        email="<EMAIL>", 
        password="agent123",
        first_name="John",
        last_name="Agent",
        user_type="agent"
    )
    print("✅ Agent user created (username: agent, password: agent123)")
else:
    print("ℹ️ Agent user already exists")

# Create buyer user
print("Creating buyer user...")
if not User.objects.filter(username="buyer").exists():
    buyer_user = User.objects.create_user(
        username="buyer",
        email="<EMAIL>",
        password="buyer123", 
        first_name="Jane",
        last_name="Buyer",
        user_type="buyer"
    )
    print("✅ Buyer user created (username: buyer, password: buyer123)")
else:
    print("ℹ️ Buyer user already exists")

# Create property categories
print("Creating property categories...")
categories = [
    {"name": "Residential", "slug": "residential", "description": "Residential properties"},
    {"name": "Commercial", "slug": "commercial", "description": "Commercial properties"},
    {"name": "Luxury", "slug": "luxury", "description": "Luxury properties"},
    {"name": "Vacation", "slug": "vacation", "description": "Vacation properties"},
]

for cat_data in categories:
    category, created = PropertyCategory.objects.get_or_create(
        slug=cat_data["slug"],
        defaults=cat_data
    )
    if created:
        print(f"✅ Created category: {category.name}")

print("🎉 Sample data created successfully!")
'''
    
    # Write and execute sample data script
    with open('create_sample_data.py', 'w') as f:
        f.write(sample_data_script)
    
    result = run_command(f"{python_cmd} create_sample_data.py", "Creating sample users and data")
    
    # Clean up script file
    if os.path.exists('create_sample_data.py'):
        os.remove('create_sample_data.py')
    
    return result is not None

def check_optional_services():
    """Check for optional services."""
    print_status("Checking optional services...")
    
    # Check Redis
    try:
        subprocess.run(['redis-cli', 'ping'], capture_output=True, check=True)
        print_success("Redis is running")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print_warning("Redis is not running (optional for real-time features)")
        print("  Install Redis:")
        print("    Ubuntu/Debian: sudo apt-get install redis-server")
        print("    macOS: brew install redis")
        print("    Windows: https://redis.io/download")

def display_final_instructions(activate_cmd):
    """Display final setup instructions."""
    print(f"\n{Colors.GREEN}")
    print("=" * 70)
    print("🎉 LuxEstate Setup Complete!")
    print("=" * 70)
    print(f"{Colors.NC}")
    
    print(f"\n{Colors.CYAN}📋 Next Steps:{Colors.NC}")
    print(f"1. Activate virtual environment: {Colors.YELLOW}{activate_cmd}{Colors.NC}")
    print(f"2. Start development server: {Colors.YELLOW}python manage.py runserver{Colors.NC}")
    print(f"3. Visit your application:")
    print(f"   • Frontend: {Colors.BLUE}http://127.0.0.1:8000/{Colors.NC}")
    print(f"   • Admin Panel: {Colors.BLUE}http://127.0.0.1:8000/admin/{Colors.NC}")
    print(f"   • API Root: {Colors.BLUE}http://127.0.0.1:8000/api/{Colors.NC}")
    
    print(f"\n{Colors.CYAN}👤 Default Login Credentials:{Colors.NC}")
    print(f"   • Admin: {Colors.GREEN}username=admin, password=admin123{Colors.NC}")
    print(f"   • Agent: {Colors.GREEN}username=agent, password=agent123{Colors.NC}")
    print(f"   • Buyer: {Colors.GREEN}username=buyer, password=buyer123{Colors.NC}")
    
    print(f"\n{Colors.CYAN}🔧 Optional Services:{Colors.NC}")
    print("   • Redis: For real-time messaging and background tasks")
    print("   • Celery worker: celery -A luxestate_backend worker -l info")
    print("   • Celery beat: celery -A luxestate_backend beat -l info")
    
    print(f"\n{Colors.CYAN}📚 Features Available:{Colors.NC}")
    print("   ✅ Property management (CRUD operations)")
    print("   ✅ User authentication and profiles")
    print("   ✅ Real-time messaging system")
    print("   ✅ Property search and filtering")
    print("   ✅ Favorites system")
    print("   ✅ Analytics and reporting")
    print("   ✅ Admin panel for management")
    print("   ✅ RESTful API endpoints")
    
    print(f"\n{Colors.GREEN}🚀 Happy coding!{Colors.NC}")
    print("=" * 70)

def main():
    """Main setup function."""
    try:
        print_header()
        
        # Check system requirements
        check_system_requirements()
        
        # Setup virtual environment
        python_cmd, activate_cmd, pip_cmd = setup_virtual_environment()
        
        # Create environment file
        create_environment_file()
        
        # Setup Django project
        setup_django_project(python_cmd)
        
        # Create sample data
        create_sample_data(python_cmd)
        
        # Check optional services
        check_optional_services()
        
        # Display final instructions
        display_final_instructions(activate_cmd)
        
    except KeyboardInterrupt:
        print(f"\n\n{Colors.YELLOW}❌ Setup interrupted by user{Colors.NC}")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n{Colors.RED}❌ Setup failed with error: {e}{Colors.NC}")
        sys.exit(1)

if __name__ == "__main__":
    main()
