"""
Views for the properties app.
"""

from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticatedOrReadOnly, IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Avg, Max
from django.utils import timezone
from django.contrib.auth import get_user_model

from .models import Property, PropertyCategory, PropertyImage, PropertyView
from .serializers import (
    PropertyListSerializer, PropertyDetailSerializer, PropertyCreateUpdateSerializer,
    PropertyCategorySerializer, PropertyImageSerializer, PropertySearchSerializer,
    PropertyViewSerializer
)
from .permissions import IsOwnerOrReadOnly, IsAgentOrOwnerOrReadOnly
from .filters import PropertyFilter

User = get_user_model()

class PropertyCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for property categories."""
    
    queryset = PropertyCategory.objects.filter(is_active=True)
    serializer_class = PropertyCategorySerializer
    lookup_field = 'slug'

class PropertyViewSet(viewsets.ModelViewSet):
    """ViewSet for properties with full CRUD operations."""
    
    queryset = Property.objects.select_related('owner', 'agent', 'category').prefetch_related('images', 'tags')
    permission_classes = [IsAuthenticatedOrReadOnly, IsOwnerOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = PropertyFilter
    search_fields = ['title', 'description', 'address', 'city', 'state']
    ordering_fields = ['created_at', 'price', 'area', 'views_count', 'favorites_count']
    ordering = ['-created_at']
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return PropertyListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return PropertyCreateUpdateSerializer
        return PropertyDetailSerializer
    
    def get_queryset(self):
        """Filter queryset based on user permissions."""
        queryset = self.queryset
        
        # Only show active properties to non-owners
        if not self.request.user.is_authenticated:
            queryset = queryset.filter(status='active')
        elif not self.request.user.is_staff:
            # Show user's own properties regardless of status, active properties for others
            queryset = queryset.filter(
                Q(status='active') | Q(owner=self.request.user)
            )
        
        return queryset
    
    def perform_create(self, serializer):
        """Set the owner to the current user when creating a property."""
        serializer.save(owner=self.request.user)
    
    @action(detail=True, methods=['post'])
    def view(self, request, pk=None):
        """Record a property view."""
        property_instance = self.get_object()
        
        # Get client IP
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip_address = x_forwarded_for.split(',')[0]
        else:
            ip_address = request.META.get('REMOTE_ADDR')
        
        # Create property view record
        PropertyView.objects.create(
            property=property_instance,
            user=request.user if request.user.is_authenticated else None,
            ip_address=ip_address,
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            referrer=request.META.get('HTTP_REFERER', ''),
            session_key=request.session.session_key
        )
        
        # Increment view count
        property_instance.views_count += 1
        property_instance.save(update_fields=['views_count'])
        
        return Response({'status': 'view recorded'})
    
    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def favorite(self, request, pk=None):
        """Add/remove property from favorites."""
        property_instance = self.get_object()
        user = request.user
        
        favorite, created = user.favorites.get_or_create(property=property_instance)
        
        if created:
            property_instance.favorites_count += 1
            property_instance.save(update_fields=['favorites_count'])
            return Response({'status': 'added to favorites'})
        else:
            favorite.delete()
            property_instance.favorites_count = max(0, property_instance.favorites_count - 1)
            property_instance.save(update_fields=['favorites_count'])
            return Response({'status': 'removed from favorites'})
    
    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get featured properties."""
        queryset = self.get_queryset().filter(is_featured=True, status='active')
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = PropertyListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)
        
        serializer = PropertyListSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def premium(self, request):
        """Get premium properties."""
        queryset = self.get_queryset().filter(is_premium=True, status='active')
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = PropertyListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)
        
        serializer = PropertyListSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def search(self, request):
        """Advanced property search."""
        serializer = PropertySearchSerializer(data=request.data)
        if serializer.is_valid():
            queryset = self.get_queryset().filter(status='active')
            
            # Apply search filters
            data = serializer.validated_data
            
            if data.get('query'):
                queryset = queryset.filter(
                    Q(title__icontains=data['query']) |
                    Q(description__icontains=data['query']) |
                    Q(address__icontains=data['query']) |
                    Q(city__icontains=data['query']) |
                    Q(state__icontains=data['query'])
                )
            
            if data.get('listing_type'):
                queryset = queryset.filter(listing_type=data['listing_type'])
            
            if data.get('property_type'):
                queryset = queryset.filter(property_type=data['property_type'])
            
            if data.get('min_price'):
                queryset = queryset.filter(price__gte=data['min_price'])
            
            if data.get('max_price'):
                queryset = queryset.filter(price__lte=data['max_price'])
            
            if data.get('min_bedrooms'):
                queryset = queryset.filter(bedrooms__gte=data['min_bedrooms'])
            
            if data.get('max_bedrooms'):
                queryset = queryset.filter(bedrooms__lte=data['max_bedrooms'])
            
            if data.get('min_bathrooms'):
                queryset = queryset.filter(bathrooms__gte=data['min_bathrooms'])
            
            if data.get('max_bathrooms'):
                queryset = queryset.filter(bathrooms__lte=data['max_bathrooms'])
            
            if data.get('min_area'):
                queryset = queryset.filter(area__gte=data['min_area'])
            
            if data.get('max_area'):
                queryset = queryset.filter(area__lte=data['max_area'])
            
            if data.get('city'):
                queryset = queryset.filter(city__icontains=data['city'])
            
            if data.get('state'):
                queryset = queryset.filter(state__icontains=data['state'])
            
            if data.get('features'):
                for feature in data['features']:
                    queryset = queryset.filter(features__contains=[feature])
            
            if data.get('amenities'):
                for amenity in data['amenities']:
                    queryset = queryset.filter(amenities__contains=[amenity])
            
            if data.get('is_featured') is not None:
                queryset = queryset.filter(is_featured=data['is_featured'])
            
            if data.get('is_premium') is not None:
                queryset = queryset.filter(is_premium=data['is_premium'])
            
            # Apply ordering
            ordering = data.get('ordering', '-created_at')
            queryset = queryset.order_by(ordering)
            
            # Paginate results
            page = self.paginate_queryset(queryset)
            if page is not None:
                result_serializer = PropertyListSerializer(page, many=True, context={'request': request})
                return self.get_paginated_response(result_serializer.data)
            
            result_serializer = PropertyListSerializer(queryset, many=True, context={'request': request})
            return Response(result_serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get property statistics."""
        queryset = self.get_queryset().filter(status='active')
        
        stats = {
            'total_properties': queryset.count(),
            'for_sale': queryset.filter(listing_type='sale').count(),
            'for_rent': queryset.filter(listing_type='rent').count(),
            'featured': queryset.filter(is_featured=True).count(),
            'premium': queryset.filter(is_premium=True).count(),
            'average_price': queryset.aggregate(avg_price=Avg('price'))['avg_price'],
            'property_types': queryset.values('property_type').annotate(count=Count('id')),
            'cities': queryset.values('city').annotate(count=Count('id')).order_by('-count')[:10],
        }
        
        return Response(stats)

class PropertyImageViewSet(viewsets.ModelViewSet):
    """ViewSet for property images."""
    
    queryset = PropertyImage.objects.all()
    serializer_class = PropertyImageSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    
    def get_queryset(self):
        """Filter images by property if specified."""
        queryset = self.queryset
        property_id = self.request.query_params.get('property_id')
        if property_id:
            queryset = queryset.filter(property_id=property_id)
        return queryset.order_by('order', 'created_at')
    
    def perform_create(self, serializer):
        """Set the order when creating an image."""
        property_instance = serializer.validated_data['property']
        max_order = PropertyImage.objects.filter(property=property_instance).aggregate(
            max_order=models.Max('order')
        )['max_order'] or 0
        serializer.save(order=max_order + 1)
