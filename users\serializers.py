"""
Serializers for the users app.
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model, authenticate
from django.contrib.auth.password_validation import validate_password
from .models import UserProfile, Favorite, SearchHistory

User = get_user_model()

class UserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for user registration."""
    
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'password', 'password_confirm',
            'first_name', 'last_name', 'user_type', 'phone_number'
        ]
    
    def validate(self, attrs):
        """Validate password confirmation."""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match.")
        return attrs
    
    def create(self, validated_data):
        """Create user with encrypted password."""
        validated_data.pop('password_confirm')
        user = User.objects.create_user(**validated_data)
        return user

class UserLoginSerializer(serializers.Serializer):
    """Serializer for user login."""
    
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        """Validate user credentials."""
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(username=email, password=password)
            if not user:
                raise serializers.ValidationError('Invalid credentials.')
            if not user.is_active:
                raise serializers.ValidationError('User account is disabled.')
            attrs['user'] = user
        else:
            raise serializers.ValidationError('Must include email and password.')
        
        return attrs

class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile."""
    
    class Meta:
        model = UserProfile
        fields = [
            'license_number', 'agency_name', 'years_experience', 'specializations',
            'facebook_url', 'twitter_url', 'linkedin_url', 'instagram_url',
            'properties_sold', 'total_sales_value', 'average_rating', 'total_reviews',
            'is_verified', 'verification_date'
        ]
        read_only_fields = [
            'properties_sold', 'total_sales_value', 'average_rating', 'total_reviews',
            'is_verified', 'verification_date'
        ]

class UserSerializer(serializers.ModelSerializer):
    """Serializer for user information."""
    
    profile = UserProfileSerializer(read_only=True)
    full_name = serializers.ReadOnlyField(source='get_full_name')
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name', 'full_name',
            'user_type', 'phone_number', 'avatar', 'bio', 'location', 'website',
            'email_notifications', 'sms_notifications', 'push_notifications',
            'marketing_emails', 'profile', 'date_joined', 'last_active'
        ]
        read_only_fields = ['id', 'username', 'date_joined']

class UserUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating user information."""
    
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'phone_number', 'avatar', 'bio',
            'location', 'website', 'email_notifications', 'sms_notifications',
            'push_notifications', 'marketing_emails'
        ]

class PasswordChangeSerializer(serializers.Serializer):
    """Serializer for password change."""
    
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(write_only=True)
    
    def validate_old_password(self, value):
        """Validate old password."""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('Old password is incorrect.')
        return value
    
    def validate(self, attrs):
        """Validate new password confirmation."""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords don't match.")
        return attrs
    
    def save(self):
        """Change user password."""
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user

class FavoriteSerializer(serializers.ModelSerializer):
    """Serializer for user favorites."""
    
    property_title = serializers.ReadOnlyField(source='property.title')
    property_price = serializers.ReadOnlyField(source='property.price')
    property_image = serializers.SerializerMethodField()
    
    class Meta:
        model = Favorite
        fields = ['id', 'property', 'property_title', 'property_price', 'property_image', 'created_at']
        read_only_fields = ['id', 'created_at']
    
    def get_property_image(self, obj):
        """Get property featured image URL."""
        if obj.property.featured_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.property.featured_image.url)
            return obj.property.featured_image.url
        return None

class SearchHistorySerializer(serializers.ModelSerializer):
    """Serializer for search history."""
    
    class Meta:
        model = SearchHistory
        fields = ['id', 'search_query', 'filters_applied', 'results_count', 'created_at']
        read_only_fields = ['id', 'created_at']
