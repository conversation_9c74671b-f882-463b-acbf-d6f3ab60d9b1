/* ===== LUXURY REAL ESTATE WEBSITE STYLES ===== */

/* Custom Properties */
:root {
    --luxury-gold: #ffc107;
    --luxury-dark: #1a1a1a;
    --luxury-light: #f8f9fa;
    --luxury-accent: #6c757d;
    --transition: all 0.3s ease;
    --border-radius: 0.5rem;
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bs-gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--luxury-gold);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #e0a800;
}

/* Navigation Styles */
.luxury-nav {
    backdrop-filter: blur(10px);
    background: rgba(26, 26, 26, 0.95) !important;
    transition: var(--transition);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-size: 1.8rem !important;
    letter-spacing: -0.5px;
}

.nav-link {
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--luxury-gold) !important;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--luxury-gold);
    transition: var(--transition);
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-image: url('https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    z-index: -2;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
}

.search-container {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 900px;
    margin: 0 auto;
}

.search-container .form-control,
.search-container .form-select {
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.search-container .form-control:focus,
.search-container .form-select:focus {
    border-color: var(--luxury-gold);
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* Property Cards */
.property-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
    border: none;
}

.property-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.property-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.property-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.property-card:hover .property-image img {
    transform: scale(1.05);
}

.property-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--luxury-gold);
    color: var(--luxury-dark);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.property-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--luxury-gold);
    margin-bottom: 0.5rem;
}

.property-location {
    color: var(--luxury-accent);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.property-features {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.property-feature {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
    color: var(--luxury-accent);
}

.favorite-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    color: var(--luxury-accent);
}

.favorite-btn:hover,
.favorite-btn.active {
    background: var(--luxury-gold);
    color: white;
}

/* Swiper Customization */
.swiper {
    padding: 20px 0 50px;
}

.swiper-button-next,
.swiper-button-prev {
    color: var(--luxury-gold);
    background: white;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    background: var(--luxury-gold);
    color: white;
}

.swiper-button-next::after,
.swiper-button-prev::after {
    font-size: 20px;
    font-weight: 600;
}

.swiper-pagination-bullet {
    background: var(--luxury-gold);
    opacity: 0.5;
    width: 12px;
    height: 12px;
}

.swiper-pagination-bullet-active {
    opacity: 1;
    transform: scale(1.2);
}

/* Testimonial Cards */
.testimonial-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
    text-align: center;
    height: 100%;
    border: none;
}

.testimonial-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin: 0 auto 1rem;
    border: 3px solid var(--luxury-gold);
}

.testimonial-rating {
    color: var(--luxury-gold);
    margin-bottom: 1rem;
}

.testimonial-text {
    font-style: italic;
    margin-bottom: 1.5rem;
    color: var(--luxury-accent);
}

.testimonial-author {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.testimonial-role {
    font-size: 0.9rem;
    color: var(--luxury-accent);
}

/* Stats Section */
.stat-item {
    padding: 2rem 1rem;
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-5px);
}

/* Floating Chat Widget */
.floating-chat {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.chat-toggle {
    width: 60px;
    height: 60px;
    background: var(--luxury-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--box-shadow-lg);
    transition: var(--transition);
    color: var(--luxury-dark);
    font-size: 1.5rem;
}

.chat-toggle:hover {
    transform: scale(1.1);
    background: #e0a800;
}

.chat-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 300px;
    background: var(--luxury-dark);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    display: none;
    overflow: hidden;
}

.chat-window.show {
    display: block;
    animation: slideUp 0.3s ease;
}

.chat-header {
    background: var(--luxury-gold);
    color: var(--luxury-dark);
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-body {
    padding: 1rem;
    color: white;
}

.quick-actions .btn {
    border-color: var(--luxury-gold);
    color: var(--luxury-gold);
    transition: var(--transition);
}

.quick-actions .btn:hover {
    background: var(--luxury-gold);
    color: var(--luxury-dark);
}

/* Animations */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Filter Sidebar */
.filter-sidebar {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    height: fit-content;
    position: sticky;
    top: 100px;
}

.filter-group {
    margin-bottom: 2rem;
}

.filter-group h6 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--luxury-dark);
}

.price-range-slider {
    margin: 1rem 0;
}

/* Form Styles */
.form-control:focus,
.form-select:focus {
    border-color: var(--luxury-gold);
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.btn-warning {
    background: var(--luxury-gold);
    border-color: var(--luxury-gold);
    color: var(--luxury-dark);
    font-weight: 600;
    transition: var(--transition);
}

.btn-warning:hover {
    background: #e0a800;
    border-color: #e0a800;
    color: var(--luxury-dark);
    transform: translateY(-2px);
}

.btn-outline-warning {
    border-color: var(--luxury-gold);
    color: var(--luxury-gold);
    transition: var(--transition);
}

.btn-outline-warning:hover {
    background: var(--luxury-gold);
    border-color: var(--luxury-gold);
    color: var(--luxury-dark);
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        min-height: 80vh;
    }
    
    .search-container {
        margin: 2rem 1rem;
    }
    
    .search-container .row {
        gap: 1rem;
    }
    
    .floating-chat {
        bottom: 20px;
        right: 20px;
    }
    
    .chat-window {
        width: 280px;
    }
    
    .property-features {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .stat-item {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .display-3 {
        font-size: 2.5rem;
    }
    
    .display-5 {
        font-size: 2rem;
    }
    
    .search-container {
        padding: 1.5rem;
    }
    
    .property-card {
        margin-bottom: 1.5rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 193, 7, 0.3);
    border-radius: 50%;
    border-top-color: var(--luxury-gold);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-luxury-gold {
    color: var(--luxury-gold) !important;
}

.bg-luxury-gold {
    background-color: var(--luxury-gold) !important;
}

.border-luxury-gold {
    border-color: var(--luxury-gold) !important;
}

.shadow-luxury {
    box-shadow: var(--box-shadow-lg) !important;
}

.rounded-luxury {
    border-radius: var(--border-radius) !important;
}

.transition-luxury {
    transition: var(--transition) !important;
}
