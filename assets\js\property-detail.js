// ===== PROPERTY DETAIL PAGE FUNCTIONALITY =====

let currentProperty = null;
let propertySwiper = null;
let thumbsSwiper = null;
let propertyMap = null;

// Initialize property detail page
document.addEventListener('DOMContentLoaded', function() {
    loadPropertyDetail();
    initializePropertySwiper();
    initializeMap();
    bindPropertyDetailEvents();
});

function loadPropertyDetail() {
    const urlParams = new URLSearchParams(window.location.search);
    const propertyId = urlParams.get('id');
    
    if (!propertyId) {
        showPropertyNotFound();
        return;
    }
    
    // Get property data
    if (window.PropertyData && window.PropertyData.getPropertyById) {
        currentProperty = window.PropertyData.getPropertyById(parseInt(propertyId));
    }
    
    if (!currentProperty) {
        showPropertyNotFound();
        return;
    }
    
    // Populate property details
    populatePropertyDetails(currentProperty);
    loadPropertyImages(currentProperty);
    loadSimilarProperties(currentProperty);
    updateFavoriteButton(currentProperty.id);
}

function populatePropertyDetails(property) {
    // Update page title and meta
    document.title = `${property.title} - LuxEstate`;
    
    // Update breadcrumb
    const breadcrumbTitle = document.getElementById('propertyTitle');
    if (breadcrumbTitle) {
        breadcrumbTitle.textContent = property.title;
    }
    
    // Update header information
    document.getElementById('propertyDetailTitle').textContent = property.title;
    document.getElementById('propertyDetailLocation').innerHTML = `
        <i class="bi bi-geo-alt text-warning me-2"></i>${property.location}
    `;
    document.getElementById('propertyDetailPrice').textContent = formatPrice(property.price);
    
    // Update overview stats
    document.getElementById('propertyDetailBedrooms').textContent = property.bedrooms;
    document.getElementById('propertyDetailBathrooms').textContent = property.bathrooms;
    document.getElementById('propertyDetailArea').textContent = formatNumber(property.area);
    document.getElementById('propertyDetailType').textContent = property.type;
    
    // Update description
    document.getElementById('propertyDetailDescription').textContent = property.description;
    
    // Update features
    const featuresContainer = document.getElementById('propertyDetailFeatures');
    if (featuresContainer && property.features) {
        featuresContainer.innerHTML = property.features.map(feature => `
            <div class="property-feature-item">
                <div class="property-feature-icon">
                    <i class="bi bi-check-lg"></i>
                </div>
                <span>${feature}</span>
            </div>
        `).join('');
    }
}

function loadPropertyImages(property) {
    const imagesContainer = document.getElementById('propertyDetailImages');
    const thumbsContainer = document.getElementById('propertyDetailThumbs');
    
    if (!imagesContainer || !thumbsContainer) return;
    
    const images = property.images || [property.image];
    
    // Load main images
    imagesContainer.innerHTML = images.map(image => `
        <div class="swiper-slide">
            <img src="${image}" alt="${property.title}">
        </div>
    `).join('');
    
    // Load thumbnails
    thumbsContainer.innerHTML = images.map(image => `
        <div class="swiper-slide">
            <img src="${image}" alt="${property.title}">
        </div>
    `).join('');
    
    // Reinitialize swiper after loading images
    setTimeout(() => {
        if (propertySwiper) {
            propertySwiper.update();
        }
        if (thumbsSwiper) {
            thumbsSwiper.update();
        }
    }, 100);
}

function initializePropertySwiper() {
    if (typeof Swiper === 'undefined') return;
    
    // Initialize thumbnails swiper first
    thumbsSwiper = new Swiper('.property-thumbs-swiper', {
        spaceBetween: 10,
        slidesPerView: 4,
        freeMode: true,
        watchSlidesProgress: true,
        breakpoints: {
            640: {
                slidesPerView: 6,
            },
            768: {
                slidesPerView: 8,
            },
        },
    });
    
    // Initialize main swiper
    propertySwiper = new Swiper('.property-detail-swiper', {
        spaceBetween: 10,
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        thumbs: {
            swiper: thumbsSwiper,
        },
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        },
    });
}

function initializeMap() {
    if (typeof L === 'undefined') return;
    
    // Initialize map
    propertyMap = L.map('propertyMap').setView([40.7128, -74.0060], 13);
    
    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(propertyMap);
    
    // Add marker for property location
    if (currentProperty) {
        const marker = L.marker([40.7128, -74.0060]).addTo(propertyMap);
        marker.bindPopup(`
            <div class="text-center">
                <h6 class="fw-bold mb-2">${currentProperty.title}</h6>
                <p class="mb-1">${currentProperty.location}</p>
                <p class="text-warning fw-bold mb-0">${formatPrice(currentProperty.price)}</p>
            </div>
        `);
    }
    
    // Resize map when container changes
    setTimeout(() => {
        propertyMap.invalidateSize();
    }, 100);
}

function loadSimilarProperties(property) {
    if (!window.PropertyData || !window.PropertyData.getSimilarProperties) return;
    
    const similarProperties = window.PropertyData.getSimilarProperties(property.id, 3);
    const container = document.getElementById('similarProperties');
    
    if (!container) return;
    
    if (similarProperties.length === 0) {
        container.innerHTML = '<div class="col-12"><p class="text-muted">No similar properties found.</p></div>';
        return;
    }
    
    container.innerHTML = similarProperties.map(prop => `
        <div class="col-md-6 col-lg-12 col-xl-6">
            ${window.PropertyData.createPropertyCard(prop)}
        </div>
    `).join('');
}

function bindPropertyDetailEvents() {
    // Favorite button
    const favoriteBtn = document.getElementById('favoriteBtn');
    if (favoriteBtn) {
        favoriteBtn.addEventListener('click', function() {
            if (currentProperty) {
                toggleFavorite(this);
            }
        });
    }
    
    // Schedule viewing form
    const scheduleModal = document.getElementById('scheduleModal');
    if (scheduleModal) {
        scheduleModal.addEventListener('show.bs.modal', function() {
            // Set minimum date to today
            const dateInput = document.getElementById('scheduleDate');
            if (dateInput) {
                const today = new Date().toISOString().split('T')[0];
                dateInput.min = today;
            }
        });
    }
}

function updateFavoriteButton(propertyId) {
    const favoriteBtn = document.getElementById('favoriteBtn');
    if (!favoriteBtn) return;
    
    const favorites = getFavorites();
    const isFavorite = favorites.includes(propertyId.toString());
    
    favoriteBtn.setAttribute('data-property-id', propertyId);
    
    if (isFavorite) {
        favoriteBtn.classList.add('active');
        favoriteBtn.innerHTML = '<i class="bi bi-heart-fill"></i> Remove from Favorites';
    } else {
        favoriteBtn.classList.remove('active');
        favoriteBtn.innerHTML = '<i class="bi bi-heart"></i> Add to Favorites';
    }
}

function submitScheduleForm() {
    const form = document.querySelector('#scheduleModal form');
    if (!form) return;
    
    // Validate form
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    // Get form data
    const formData = new FormData(form);
    const scheduleData = {
        propertyId: currentProperty.id,
        propertyTitle: currentProperty.title,
        name: formData.get('scheduleName') || document.getElementById('scheduleName').value,
        email: formData.get('scheduleEmail') || document.getElementById('scheduleEmail').value,
        phone: formData.get('schedulePhone') || document.getElementById('schedulePhone').value,
        date: formData.get('scheduleDate') || document.getElementById('scheduleDate').value,
        time: formData.get('scheduleTime') || document.getElementById('scheduleTime').value,
        notes: formData.get('scheduleNotes') || document.getElementById('scheduleNotes').value
    };
    
    // Simulate API call
    const submitBtn = document.querySelector('#scheduleModal .btn-warning');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="loading"></span> Scheduling...';
    submitBtn.disabled = true;
    
    setTimeout(() => {
        // Show success message
        if (window.LuxEstate && window.LuxEstate.showToast) {
            window.LuxEstate.showToast(
                'Viewing Scheduled!',
                `We'll contact you soon to confirm your viewing for ${currentProperty.title}`,
                'success'
            );
        }
        
        // Reset form and close modal
        form.reset();
        form.classList.remove('was-validated');
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleModal'));
        if (modal) {
            modal.hide();
        }
        
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

function showPropertyNotFound() {
    document.body.innerHTML = `
        <div class="container mt-5 pt-5">
            <div class="row justify-content-center">
                <div class="col-md-6 text-center">
                    <i class="bi bi-house-x display-1 text-muted mb-4"></i>
                    <h1 class="display-4 fw-bold mb-3">Property Not Found</h1>
                    <p class="lead text-muted mb-4">The property you're looking for doesn't exist or has been removed.</p>
                    <div class="d-flex gap-3 justify-content-center">
                        <a href="listings.html" class="btn btn-warning">Browse Properties</a>
                        <a href="index.html" class="btn btn-outline-secondary">Go Home</a>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Utility functions for property detail
function formatPrice(price) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(price);
}

function formatNumber(num) {
    return new Intl.NumberFormat('en-US').format(num);
}

function getFavorites() {
    return JSON.parse(localStorage.getItem('favorites') || '[]');
}

function toggleFavorite(btn) {
    const propertyId = btn.getAttribute('data-property-id');
    const isActive = btn.classList.contains('active');
    
    if (isActive) {
        btn.classList.remove('active');
        btn.innerHTML = '<i class="bi bi-heart"></i> Add to Favorites';
        removeFromFavorites(propertyId);
        if (window.LuxEstate && window.LuxEstate.showToast) {
            window.LuxEstate.showToast('Removed', 'Property removed from favorites.', 'info');
        }
    } else {
        btn.classList.add('active');
        btn.innerHTML = '<i class="bi bi-heart-fill"></i> Remove from Favorites';
        addToFavorites(propertyId);
        if (window.LuxEstate && window.LuxEstate.showToast) {
            window.LuxEstate.showToast('Added', 'Property added to favorites.', 'success');
        }
    }
}

function addToFavorites(propertyId) {
    let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    if (!favorites.includes(propertyId)) {
        favorites.push(propertyId);
        localStorage.setItem('favorites', JSON.stringify(favorites));
    }
}

function removeFromFavorites(propertyId) {
    let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    favorites = favorites.filter(id => id !== propertyId);
    localStorage.setItem('favorites', JSON.stringify(favorites));
}

// Make functions globally available
window.PropertyDetail = {
    submitScheduleForm,
    toggleFavorite,
    loadPropertyDetail,
    currentProperty
};
