"""
Property models for the LuxEstate application.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.text import slugify
from imagekit.models import ProcessedImageField
from imagekit.processors import ResizeToFill
from taggit.managers import TaggableManager
from mptt.models import MPTTModel, TreeForeignKey

User = get_user_model()

class PropertyCategory(MPTTModel):
    """Hierarchical property categories."""
    
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True)
    parent = TreeForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='children')
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True, help_text="Bootstrap icon class")
    is_active = models.BooleanField(default=True)
    
    class MPTTMeta:
        order_insertion_by = ['name']
    
    class Meta:
        db_table = 'property_categories'
        verbose_name = 'Property Category'
        verbose_name_plural = 'Property Categories'
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

class Property(models.Model):
    """Main property model."""
    
    LISTING_TYPES = (
        ('sale', 'For Sale'),
        ('rent', 'For Rent'),
        ('both', 'Sale & Rent'),
    )
    
    PROPERTY_TYPES = (
        ('house', 'House'),
        ('apartment', 'Apartment'),
        ('villa', 'Villa'),
        ('penthouse', 'Penthouse'),
        ('mansion', 'Mansion'),
        ('townhouse', 'Townhouse'),
        ('loft', 'Loft'),
        ('chalet', 'Chalet'),
        ('condo', 'Condominium'),
        ('studio', 'Studio'),
    )
    
    STATUS_CHOICES = (
        ('active', 'Active'),
        ('pending', 'Pending'),
        ('sold', 'Sold'),
        ('rented', 'Rented'),
        ('inactive', 'Inactive'),
    )
    
    # Basic Information
    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True)
    description = models.TextField()
    category = models.ForeignKey(PropertyCategory, on_delete=models.SET_NULL, null=True, blank=True)
    property_type = models.CharField(max_length=20, choices=PROPERTY_TYPES)
    listing_type = models.CharField(max_length=10, choices=LISTING_TYPES, default='sale')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    
    # Pricing
    price = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(0)])
    rent_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, validators=[MinValueValidator(0)])
    price_per_sqft = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    # Location
    address = models.CharField(max_length=255)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    country = models.CharField(max_length=100, default='United States')
    zip_code = models.CharField(max_length=20)
    latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)
    
    # Property Details
    bedrooms = models.PositiveIntegerField(validators=[MinValueValidator(0), MaxValueValidator(50)])
    bathrooms = models.DecimalField(max_digits=3, decimal_places=1, validators=[MinValueValidator(0), MaxValueValidator(50)])
    area = models.PositiveIntegerField(help_text="Area in square feet", validators=[MinValueValidator(1)])
    lot_size = models.PositiveIntegerField(null=True, blank=True, help_text="Lot size in square feet")
    year_built = models.PositiveIntegerField(null=True, blank=True, validators=[MinValueValidator(1800), MaxValueValidator(2030)])
    floors = models.PositiveIntegerField(null=True, blank=True, validators=[MinValueValidator(1), MaxValueValidator(20)])
    parking_spaces = models.PositiveIntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(20)])
    
    # Features and Amenities
    features = models.JSONField(default=list, blank=True)
    amenities = models.JSONField(default=list, blank=True)
    
    # Media
    featured_image = ProcessedImageField(
        upload_to='properties/featured/',
        processors=[ResizeToFill(800, 600)],
        format='JPEG',
        options={'quality': 90},
        null=True,
        blank=True
    )
    virtual_tour_url = models.URLField(blank=True)
    video_url = models.URLField(blank=True)
    
    # SEO and Marketing
    meta_title = models.CharField(max_length=200, blank=True)
    meta_description = models.TextField(max_length=300, blank=True)
    tags = TaggableManager(blank=True)
    is_featured = models.BooleanField(default=False)
    is_premium = models.BooleanField(default=False)
    
    # Ownership and Management
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='owned_properties')
    agent = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_properties')
    
    # Statistics
    views_count = models.PositiveIntegerField(default=0)
    inquiries_count = models.PositiveIntegerField(default=0)
    favorites_count = models.PositiveIntegerField(default=0)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'properties'
        verbose_name = 'Property'
        verbose_name_plural = 'Properties'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'listing_type']),
            models.Index(fields=['city', 'state']),
            models.Index(fields=['price']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return self.title
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        
        # Calculate price per sqft
        if self.price and self.area:
            self.price_per_sqft = self.price / self.area
        
        super().save(*args, **kwargs)
    
    @property
    def location(self):
        """Return formatted location string."""
        return f"{self.city}, {self.state}"
    
    @property
    def full_address(self):
        """Return full formatted address."""
        return f"{self.address}, {self.city}, {self.state} {self.zip_code}"
    
    def get_absolute_url(self):
        """Return the absolute URL for the property."""
        return f"/property/{self.slug}/"

class PropertyImage(models.Model):
    """Property images."""
    
    property = models.ForeignKey(Property, on_delete=models.CASCADE, related_name='images')
    image = ProcessedImageField(
        upload_to='properties/images/',
        processors=[ResizeToFill(1200, 800)],
        format='JPEG',
        options={'quality': 90}
    )
    thumbnail = ProcessedImageField(
        upload_to='properties/thumbnails/',
        processors=[ResizeToFill(300, 200)],
        format='JPEG',
        options={'quality': 80}
    )
    caption = models.CharField(max_length=200, blank=True)
    alt_text = models.CharField(max_length=200, blank=True)
    order = models.PositiveIntegerField(default=0)
    is_featured = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'property_images'
        ordering = ['order', 'created_at']
        verbose_name = 'Property Image'
        verbose_name_plural = 'Property Images'
    
    def __str__(self):
        return f"{self.property.title} - Image {self.order}"

class PropertyView(models.Model):
    """Track property views for analytics."""
    
    property = models.ForeignKey(Property, on_delete=models.CASCADE, related_name='property_views')
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    referrer = models.URLField(blank=True)
    session_key = models.CharField(max_length=40, blank=True)
    viewed_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'property_views'
        verbose_name = 'Property View'
        verbose_name_plural = 'Property Views'
        indexes = [
            models.Index(fields=['property', 'viewed_at']),
            models.Index(fields=['ip_address', 'viewed_at']),
        ]
    
    def __str__(self):
        user_info = self.user.get_full_name() if self.user else 'Anonymous'
        return f"{self.property.title} - {user_info}"
