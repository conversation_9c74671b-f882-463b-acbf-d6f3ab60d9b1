<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - LuxEstate</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- AOS CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/themes.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">

    <style>
        .contact-card {
            background: var(--bs-body-bg);
            border: 1px solid var(--bs-border-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
            transition: var(--transition);
            height: 100%;
        }

        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--box-shadow-lg);
        }

        .contact-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 193, 7, 0.1);
            color: var(--luxury-gold);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
        }

        .map-container {
            height: 400px;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }

        .office-card {
            background: var(--bs-body-bg);
            border: 1px solid var(--bs-border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: var(--transition);
        }

        .office-card:hover {
            box-shadow: var(--box-shadow);
        }

        .office-image {
            width: 80px;
            height: 80px;
            border-radius: var(--border-radius);
            object-fit: cover;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top luxury-nav">
        <div class="container">
            <a class="navbar-brand fw-bold fs-3" href="index.html">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Desktop Navigation -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="listings.html">Properties</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.html">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.html">Contact</a>
                    </li>
                </ul>

                <div class="d-flex align-items-center">
                    <!-- Theme Switcher -->
                    <div class="dropdown me-3">
                        <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-palette"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item theme-option" href="#" data-theme="light">
                                <i class="bi bi-sun"></i> Light
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="dark">
                                <i class="bi bi-moon"></i> Dark
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="luxury">
                                <i class="bi bi-gem"></i> Luxury
                            </a></li>
                        </ul>
                    </div>

                    <a href="favorites.html" class="btn btn-outline-light btn-sm me-2">
                        <i class="bi bi-heart"></i> <span class="d-none d-md-inline">Favorites</span>
                    </a>
                    <a href="login.html" class="btn btn-warning btn-sm me-2">Login</a>
                    <a href="add-property.html" class="btn btn-outline-warning btn-sm">
                        <i class="bi bi-plus-circle"></i> <span class="d-none d-md-inline">Add Property</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Navigation Offcanvas -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="mobileNav">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="index.html">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="listings.html">Properties</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="about.html">About</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="blog.html">Blog</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="contact.html">Contact</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="favorites.html">Favorites</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.html">Dashboard</a>
                </li>
            </ul>
            <hr>
            <div class="d-grid gap-2">
                <a href="login.html" class="btn btn-warning">Login</a>
                <a href="add-property.html" class="btn btn-outline-warning">Add Property</a>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="py-5 mt-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                    <h1 class="display-4 fw-bold mb-4">Get In Touch</h1>
                    <p class="lead text-muted">Ready to find your dream property or have questions about our services? We're here to help you every step of the way.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Methods -->
    <section class="py-5">
        <div class="container">
            <div class="row g-4 mb-5">
                <div class="col-lg-4 col-md-6" data-aos="fade-up">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="bi bi-telephone"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Call Us</h4>
                        <p class="text-muted mb-3">Speak directly with our luxury property specialists</p>
                        <h5 class="text-warning mb-2">+****************</h5>
                        <p class="text-muted small">Mon - Fri: 9AM - 8PM<br>Sat - Sun: 10AM - 6PM</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="bi bi-envelope"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Email Us</h4>
                        <p class="text-muted mb-3">Send us your inquiries and we'll respond within 24 hours</p>
                        <h5 class="text-warning mb-2"><EMAIL></h5>
                        <p class="text-muted small">For general inquiries<br><EMAIL> for sales</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="bi bi-geo-alt"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Visit Us</h4>
                        <p class="text-muted mb-3">Come to our flagship office in the heart of Manhattan</p>
                        <h5 class="text-warning mb-2">123 Luxury Avenue</h5>
                        <p class="text-muted small">Premium District<br>New York, NY 10001</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form & Map -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row g-5">
                <!-- Contact Form -->
                <div class="col-lg-6" data-aos="fade-right">
                    <div class="card border-0 shadow">
                        <div class="card-body p-4">
                            <h3 class="fw-bold mb-4">Send Us a Message</h3>

                            <form class="needs-validation" novalidate data-form-type="contact">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="firstName" class="form-label">First Name *</label>
                                        <input type="text" class="form-control" id="firstName" required>
                                        <div class="invalid-feedback">Please enter your first name.</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="lastName" class="form-label">Last Name *</label>
                                        <input type="text" class="form-control" id="lastName" required>
                                        <div class="invalid-feedback">Please enter your last name.</div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" required>
                                    <div class="invalid-feedback">Please enter a valid email address.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone">
                                </div>

                                <div class="mb-3">
                                    <label for="subject" class="form-label">Subject *</label>
                                    <select class="form-select" id="subject" required>
                                        <option value="">Select a subject</option>
                                        <option value="general">General Inquiry</option>
                                        <option value="buying">Buying Property</option>
                                        <option value="selling">Selling Property</option>
                                        <option value="viewing">Schedule Viewing</option>
                                        <option value="investment">Investment Opportunities</option>
                                        <option value="partnership">Partnership</option>
                                        <option value="other">Other</option>
                                    </select>
                                    <div class="invalid-feedback">Please select a subject.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="message" class="form-label">Message *</label>
                                    <textarea class="form-control" id="message" rows="5" required placeholder="Tell us how we can help you..."></textarea>
                                    <div class="invalid-feedback">Please enter your message.</div>
                                </div>

                                <div class="form-check mb-4">
                                    <input class="form-check-input" type="checkbox" id="newsletter">
                                    <label class="form-check-label" for="newsletter">
                                        Subscribe to our newsletter for luxury property updates
                                    </label>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-warning btn-lg">
                                        <i class="bi bi-send me-2"></i>Send Message
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Map & Office Info -->
                <div class="col-lg-6" data-aos="fade-left">
                    <div class="mb-4">
                        <h3 class="fw-bold mb-4">Find Our Offices</h3>
                        <div class="map-container" id="contactMap"></div>
                    </div>

                    <!-- Office Locations -->
                    <div class="office-locations">
                        <div class="office-card">
                            <div class="d-flex align-items-center">
                                <img src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80" alt="New York Office" class="office-image me-3">
                                <div>
                                    <h5 class="fw-bold mb-1">New York (Headquarters)</h5>
                                    <p class="text-muted mb-1">123 Luxury Avenue, Premium District</p>
                                    <p class="text-muted mb-0">+****************</p>
                                </div>
                            </div>
                        </div>

                        <div class="office-card">
                            <div class="d-flex align-items-center">
                                <img src="https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80" alt="London Office" class="office-image me-3">
                                <div>
                                    <h5 class="fw-bold mb-1">London</h5>
                                    <p class="text-muted mb-1">45 Mayfair Street, London W1K 5DB</p>
                                    <p class="text-muted mb-0">+44 20 7123 4567</p>
                                </div>
                            </div>
                        </div>

                        <div class="office-card">
                            <div class="d-flex align-items-center">
                                <img src="https://images.unsplash.com/photo-1512453979798-5ea266f8880c?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80" alt="Dubai Office" class="office-image me-3">
                                <div>
                                    <h5 class="fw-bold mb-1">Dubai</h5>
                                    <p class="text-muted mb-1">Dubai Marina, UAE</p>
                                    <p class="text-muted mb-0">+971 4 123 4567</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <h3 class="fw-bold text-center mb-5" data-aos="fade-up">Frequently Asked Questions</h3>

                    <div class="accordion" id="faqAccordion" data-aos="fade-up" data-aos-delay="200">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    How do I schedule a property viewing?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    You can schedule a viewing by clicking the "Schedule Viewing" button on any property page, calling us directly, or using the contact form above. We'll arrange a convenient time for you to visit the property with one of our specialists.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    What areas do you cover?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    We operate in over 50 major cities worldwide, including New York, London, Dubai, Hong Kong, Los Angeles, Miami, and many more. Our global network ensures we can help you find luxury properties anywhere in the world.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    Do you offer property management services?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Yes, we offer comprehensive property management services for luxury properties, including maintenance, tenant screening, rent collection, and investment property management. Contact us to learn more about our management packages.
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                    How do I list my property with LuxEstate?
                                </button>
                            </h2>
                            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    You can list your property by using our "Add Property" form, contacting us directly, or scheduling a consultation. Our team will evaluate your property, provide a market analysis, and create a comprehensive marketing strategy to attract qualified buyers.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="bi bi-building text-warning me-2"></i>LuxEstate
                    </h5>
                    <p class="text-muted">Your premier destination for luxury real estate worldwide. We connect discerning clients with exceptional properties.</p>
                    <div class="social-links">
                        <a href="#" class="text-warning me-3"><i class="bi bi-facebook fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-twitter fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-instagram fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-linkedin fs-4"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.html" class="text-muted text-decoration-none">Home</a></li>
                        <li><a href="listings.html" class="text-muted text-decoration-none">Properties</a></li>
                        <li><a href="about.html" class="text-muted text-decoration-none">About</a></li>
                        <li><a href="contact.html" class="text-muted text-decoration-none">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Services</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted text-decoration-none">Buy Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Sell Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Rent Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Property Management</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Contact Info</h6>
                    <div class="contact-info">
                        <p class="text-muted mb-2">
                            <i class="bi bi-geo-alt text-warning me-2"></i>
                            123 Luxury Avenue, Premium District, NY 10001
                        </p>
                        <p class="text-muted mb-2">
                            <i class="bi bi-telephone text-warning me-2"></i>
                            +****************
                        </p>
                        <p class="text-muted mb-2">
                            <i class="bi bi-envelope text-warning me-2"></i>
                            <EMAIL>
                        </p>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 LuxEstate. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-muted text-decoration-none me-3">Privacy Policy</a>
                    <a href="#" class="text-muted text-decoration-none">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating Chat Widget -->
    <div class="floating-chat" id="floatingChat">
        <div class="chat-toggle" onclick="toggleChat()">
            <i class="bi bi-chat-dots"></i>
        </div>
        <div class="chat-window" id="chatWindow">
            <div class="chat-header">
                <h6 class="mb-0">Need Help?</h6>
                <button class="btn-close btn-close-white" onclick="toggleChat()"></button>
            </div>
            <div class="chat-body">
                <p class="mb-2">Hi! How can we help you today?</p>
                <div class="quick-actions">
                    <button class="btn btn-sm btn-outline-light mb-2 w-100">Schedule Consultation</button>
                    <button class="btn btn-sm btn-outline-light mb-2 w-100">Property Inquiry</button>
                    <button class="btn btn-sm btn-outline-light w-100">Speak to Agent</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- AOS JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/theme-switcher.js"></script>

    <script>
        // Initialize map
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof L !== 'undefined') {
                const map = L.map('contactMap').setView([40.7128, -74.0060], 13);

                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors'
                }).addTo(map);

                // Add marker for main office
                L.marker([40.7128, -74.0060]).addTo(map)
                    .bindPopup('<div class="text-center"><h6 class="fw-bold mb-1">LuxEstate HQ</h6><p class="mb-0">123 Luxury Avenue<br>New York, NY 10001</p></div>');

                // Resize map when container changes
                setTimeout(() => {
                    map.invalidateSize();
                }, 100);
            }
        });
    </script>
</body>
</html>