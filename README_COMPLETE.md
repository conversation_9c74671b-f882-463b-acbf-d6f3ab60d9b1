# 🏠 LuxEstate - Complete Luxury Real Estate Platform

A comprehensive luxury real estate platform built with Django REST Framework, Bootstrap 5, and modern web technologies. **No payment gateways included** - focused on core real estate functionality.

## ✨ Features Overview

### 🏘️ Property Management
- **Complete CRUD Operations** for properties with sale/rent listings
- **Advanced Search & Filtering** with 15+ criteria (price, location, type, etc.)
- **Property Categories** with hierarchical structure
- **Image Gallery** with automatic thumbnail generation
- **Virtual Tours** and video integration support
- **Featured & Premium** property promotions
- **Property Analytics** and performance tracking
- **Property Views** tracking and statistics

### 👥 User Management System
- **Multi-role Authentication** (Buyers, Sellers, Agents, Admins)
- **Comprehensive User Profiles** with social media integration
- **User Registration/Login** with email verification
- **Favorites System** with localStorage synchronization
- **Search History** tracking for analytics
- **Notification Preferences** management
- **Agent Verification** system

### 💬 Communication System
- **Real-time Messaging** with WebSocket support
- **Property Inquiries** with automated notifications
- **Conversation Management** between users
- **Email Integration** for important updates
- **Notification System** for all platform activities
- **Message Read/Unread** status tracking

### 📊 Analytics & Reporting
- **Property Performance** metrics and insights
- **User Activity** tracking and analysis
- **Market Trends** analysis and reporting
- **Search Analytics** for platform optimization
- **Custom Report** generation
- **Dashboard Analytics** for all user types
- **Real-time Statistics** updates

### 🎨 Frontend Features
- **Responsive Design** with Bootstrap 5
- **Theme Switching** (Light, Dark, Luxury themes)
- **Interactive Property** cards and listings
- **Advanced Search** interface
- **User Dashboard** with comprehensive management
- **Real-time Chat** interface
- **Property Comparison** tools
- **Favorites Management** interface

## 🚀 Quick Installation

### Option 1: One-Click Setup (Recommended)

```bash
# Make the setup script executable and run it
chmod +x setup_luxestate.py
python setup_luxestate.py
```

### Option 2: Platform-Specific Scripts

**Linux/macOS:**
```bash
chmod +x install.sh
./install.sh
```

**Windows:**
```cmd
install.bat
```

### Option 3: Manual Installation

```bash
# 1. Create virtual environment
python -m venv luxestate_env

# 2. Activate virtual environment
# Linux/macOS:
source luxestate_env/bin/activate
# Windows:
luxestate_env\Scripts\activate.bat

# 3. Install dependencies
pip install -r requirements.txt

# 4. Setup database
python manage.py makemigrations
python manage.py migrate

# 5. Create superuser
python manage.py createsuperuser

# 6. Collect static files
python manage.py collectstatic

# 7. Start development server
python manage.py runserver
```

## 🌐 Access Your Application

After successful installation:

- **Frontend**: http://127.0.0.1:8000/
- **Admin Panel**: http://127.0.0.1:8000/admin/
- **API Root**: http://127.0.0.1:8000/api/

### Default Login Credentials
- **Admin**: `username=admin, password=admin123`
- **Agent**: `username=agent, password=agent123`
- **Buyer**: `username=buyer, password=buyer123`

## 📋 API Endpoints

### Authentication
- `POST /api/users/auth/register/` - User registration
- `POST /api/users/auth/login/` - User login
- `POST /api/users/auth/logout/` - User logout
- `GET /api/users/auth/me/` - Current user information

### Properties
- `GET /api/properties/properties/` - List all properties
- `POST /api/properties/properties/` - Create new property
- `GET /api/properties/properties/{id}/` - Property details
- `PUT /api/properties/properties/{id}/` - Update property
- `DELETE /api/properties/properties/{id}/` - Delete property
- `POST /api/properties/properties/search/` - Advanced search
- `GET /api/properties/properties/featured/` - Featured properties
- `POST /api/properties/properties/{id}/favorite/` - Toggle favorite

### Messages & Communication
- `GET /api/messages/conversations/` - List conversations
- `POST /api/messages/conversations/` - Create conversation
- `GET /api/messages/conversations/{id}/messages/` - Get messages
- `POST /api/messages/conversations/{id}/send_message/` - Send message
- `POST /api/messages/inquiries/` - Submit property inquiry
- `GET /api/messages/notifications/` - Get notifications

### Analytics
- `GET /api/analytics/dashboard/` - Dashboard analytics
- `POST /api/analytics/reports/` - Generate custom reports
- `GET /api/analytics/property-analytics/` - Property performance
- `GET /api/analytics/market-trends/` - Market trends data

### User Management
- `GET /api/users/users/me/` - Current user profile
- `PUT /api/users/users/me/` - Update user profile
- `GET /api/users/users/favorites/` - User's favorite properties
- `POST /api/users/users/add_favorite/` - Add property to favorites
- `DELETE /api/users/users/remove_favorite/` - Remove from favorites

## 🏗️ Project Structure

```
luxestate_backend/
├── 📁 luxestate_backend/        # Main Django project
│   ├── settings.py             # Django configuration
│   ├── urls.py                 # URL routing
│   ├── wsgi.py                 # WSGI configuration
│   ├── asgi.py                 # ASGI for WebSocket
│   └── celery.py               # Celery configuration
├── 📁 properties/              # Property management app
│   ├── models.py               # Property models
│   ├── views.py                # API views
│   ├── serializers.py          # DRF serializers
│   ├── filters.py              # Search filters
│   ├── permissions.py          # Custom permissions
│   ├── admin.py                # Admin interface
│   └── urls.py                 # Property URLs
├── 📁 users/                   # User management app
│   ├── models.py               # User models
│   ├── views.py                # User API views
│   ├── serializers.py          # User serializers
│   ├── admin.py                # User admin
│   └── urls.py                 # User URLs
├── 📁 messages/                # Communication app
│   ├── models.py               # Message models
│   ├── views.py                # Message API views
│   ├── serializers.py          # Message serializers
│   ├── consumers.py            # WebSocket consumers
│   ├── routing.py              # WebSocket routing
│   ├── admin.py                # Message admin
│   └── urls.py                 # Message URLs
├── 📁 analytics/               # Analytics app
│   ├── models.py               # Analytics models
│   ├── views.py                # Analytics views
│   ├── admin.py                # Analytics admin
│   └── urls.py                 # Analytics URLs
├── 📁 static/                  # Static files
│   └── assets/                 # Frontend assets
│       ├── css/                # Stylesheets
│       ├── js/                 # JavaScript files
│       └── images/             # Images
├── 📁 media/                   # User uploads
├── 📁 templates/               # HTML templates
├── 📁 logs/                    # Application logs
├── 📄 requirements.txt         # Python dependencies
├── 📄 manage.py                # Django management
├── 🔧 setup_luxestate.py       # Complete setup script
├── 🔧 install.sh               # Linux/macOS installer
├── 🔧 install.bat              # Windows installer
└── 📖 README.md                # This documentation
```

## 🔧 Configuration

### Environment Variables (.env)
```env
# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (SQLite for development)
DB_ENGINE=django.db.backends.sqlite3
DB_NAME=db.sqlite3

# Email Settings
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# Celery Settings (Optional)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Application Settings
PROPERTY_IMAGE_MAX_SIZE=5242880
PROPERTY_MAX_IMAGES=20
```

## 🔧 Optional Services

### Redis (For Real-time Features)
```bash
# Install Redis
# Ubuntu/Debian: sudo apt-get install redis-server
# macOS: brew install redis
# Windows: https://redis.io/download

# Start Redis
redis-server
```

### Celery (For Background Tasks)
```bash
# Start Celery worker
celery -A luxestate_backend worker -l info

# Start Celery beat scheduler
celery -A luxestate_backend beat -l info
```

## 🧪 Testing

```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test properties
python manage.py test users
python manage.py test messages
python manage.py test analytics
```

## 🚀 Production Deployment

### Environment Setup
```env
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
SECRET_KEY=your-production-secret-key

# PostgreSQL Database
DB_ENGINE=django.db.backends.postgresql
DB_NAME=luxestate_production
DB_USER=your_db_user
DB_PASSWORD=your_secure_password
DB_HOST=your_db_host
DB_PORT=5432
```

### Using Gunicorn
```bash
pip install gunicorn
gunicorn luxestate_backend.wsgi:application --bind 0.0.0.0:8000
```

## 📚 Key Features Implemented

### ✅ Core Functionality
- Property CRUD operations with image management
- User authentication and authorization
- Real-time messaging system
- Advanced search and filtering
- Favorites and wishlist management
- Property analytics and reporting
- Admin panel for complete management

### ✅ Advanced Features
- WebSocket support for real-time communication
- Celery integration for background tasks
- Image processing with thumbnails
- Email notification system
- Search history tracking
- Market trends analysis
- Multi-role user system

### ✅ Frontend Integration
- Bootstrap 5 responsive design
- Theme switching (Light/Dark/Luxury)
- Interactive property cards
- Real-time chat interface
- Advanced search forms
- User dashboard
- Property comparison tools

## 🆘 Troubleshooting

### Common Issues
1. **Virtual environment not activated**: Run the activation command
2. **Database errors**: Run `python manage.py migrate`
3. **Static files not loading**: Run `python manage.py collectstatic`
4. **Redis connection error**: Make sure Redis server is running
5. **Permission denied**: Check file permissions and ownership

### Getting Help
- Check Django logs in `logs/django.log`
- Use `python manage.py shell` for debugging
- Run `python manage.py check` for system checks

## 🎉 Success Indicators

After successful setup, you should have:
- ✅ Django development server running on http://127.0.0.1:8000/
- ✅ Admin panel accessible at http://127.0.0.1:8000/admin/
- ✅ API endpoints responding at http://127.0.0.1:8000/api/
- ✅ Frontend pages loading with backend integration
- ✅ User authentication working
- ✅ Property management functional
- ✅ Real-time messaging operational (with Redis)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**🏠 LuxEstate - Built for the luxury real estate industry with ❤️**
