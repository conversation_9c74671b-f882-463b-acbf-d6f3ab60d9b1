<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Properties - LuxEstate</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css">
    
    <!-- AOS CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top luxury-nav">
        <div class="container">
            <a class="navbar-brand fw-bold fs-3" href="index.html">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </a>
            
            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Desktop Navigation -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="listings.html">Properties</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.html">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">Contact</a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <!-- Theme Switcher -->
                    <div class="dropdown me-3">
                        <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-palette"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item theme-option" href="#" data-theme="light">
                                <i class="bi bi-sun"></i> Light
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="dark">
                                <i class="bi bi-moon"></i> Dark
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="luxury">
                                <i class="bi bi-gem"></i> Luxury
                            </a></li>
                        </ul>
                    </div>
                    
                    <a href="favorites.html" class="btn btn-outline-light btn-sm me-2">
                        <i class="bi bi-heart"></i> <span class="d-none d-md-inline">Favorites</span>
                    </a>
                    <a href="login.html" class="btn btn-warning btn-sm me-2">Login</a>
                    <a href="add-property.html" class="btn btn-outline-warning btn-sm">
                        <i class="bi bi-plus-circle"></i> <span class="d-none d-md-inline">Add Property</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Navigation Offcanvas -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="mobileNav">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="index.html">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="listings.html">Properties</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="about.html">About</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="blog.html">Blog</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="contact.html">Contact</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="favorites.html">Favorites</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.html">Dashboard</a>
                </li>
            </ul>
            <hr>
            <div class="d-grid gap-2">
                <a href="login.html" class="btn btn-warning">Login</a>
                <a href="add-property.html" class="btn btn-outline-warning">Add Property</a>
            </div>
        </div>
    </div>

    <!-- Page Header -->
    <section class="py-5 mt-5 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold mb-3">Luxury Properties</h1>
                    <p class="lead text-muted">Discover exceptional homes in the world's most desirable locations</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <div class="d-flex align-items-center justify-content-lg-end gap-3">
                        <span class="text-muted">Sort by:</span>
                        <select class="form-select sort-select" style="width: auto;">
                            <option value="price-desc">Price: High to Low</option>
                            <option value="price-asc">Price: Low to High</option>
                            <option value="bedrooms-desc">Most Bedrooms</option>
                            <option value="area-desc">Largest Area</option>
                            <option value="newest">Newest First</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- Filter Sidebar -->
                <div class="col-lg-3 mb-4">
                    <div class="filter-sidebar">
                        <h5 class="fw-bold mb-4">Filter Properties</h5>
                        
                        <form class="filter-form">
                            <!-- Location Filter -->
                            <div class="filter-group">
                                <h6>Location</h6>
                                <input type="text" class="form-control" name="location" placeholder="Enter city or area">
                            </div>
                            
                            <!-- Listing Type Filter -->
                            <div class="filter-group">
                                <h6>Listing Type</h6>
                                <select class="form-select" name="listingType">
                                    <option value="">For Sale & Rent</option>
                                    <option value="sale">For Sale</option>
                                    <option value="rent">For Rent</option>
                                </select>
                            </div>

                            <!-- Property Type Filter -->
                            <div class="filter-group">
                                <h6>Property Type</h6>
                                <select class="form-select" name="type">
                                    <option value="">Any Type</option>
                                    <option value="house">House</option>
                                    <option value="apartment">Apartment</option>
                                    <option value="villa">Villa</option>
                                    <option value="penthouse">Penthouse</option>
                                    <option value="mansion">Mansion</option>
                                    <option value="townhouse">Townhouse</option>
                                    <option value="loft">Loft</option>
                                    <option value="chalet">Chalet</option>
                                </select>
                            </div>
                            
                            <!-- Bedrooms Filter -->
                            <div class="filter-group">
                                <h6>Bedrooms</h6>
                                <select class="form-select" name="bedrooms">
                                    <option value="">Any</option>
                                    <option value="1">1+</option>
                                    <option value="2">2+</option>
                                    <option value="3">3+</option>
                                    <option value="4">4+</option>
                                    <option value="5">5+</option>
                                    <option value="6">6+</option>
                                </select>
                            </div>
                            
                            <!-- Price Range Filter -->
                            <div class="filter-group">
                                <h6>Price Range</h6>
                                <div class="row g-2">
                                    <div class="col-6">
                                        <input type="number" class="form-control" name="minPrice" placeholder="Min Price">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control" name="maxPrice" placeholder="Max Price">
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">Quick filters:</small>
                                    <div class="d-flex flex-wrap gap-1 mt-1">
                                        <button type="button" class="btn btn-outline-warning btn-sm price-filter" data-min="0" data-max="1000000">Under $1M</button>
                                        <button type="button" class="btn btn-outline-warning btn-sm price-filter" data-min="1000000" data-max="5000000">$1M - $5M</button>
                                        <button type="button" class="btn btn-outline-warning btn-sm price-filter" data-min="5000000" data-max="">$5M+</button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Features Filter -->
                            <div class="filter-group">
                                <h6>Features</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="features" value="pool" id="pool">
                                    <label class="form-check-label" for="pool">Pool</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="features" value="garage" id="garage">
                                    <label class="form-check-label" for="garage">Garage</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="features" value="garden" id="garden">
                                    <label class="form-check-label" for="garden">Garden</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="features" value="fireplace" id="fireplace">
                                    <label class="form-check-label" for="fireplace">Fireplace</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="features" value="balcony" id="balcony">
                                    <label class="form-check-label" for="balcony">Balcony</label>
                                </div>
                            </div>
                            
                            <!-- Filter Actions -->
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-warning">Apply Filters</button>
                                <button type="reset" class="btn btn-outline-secondary">Clear All</button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Properties Grid -->
                <div class="col-lg-9">
                    <!-- Results Info -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div class="results-info">
                            <span class="fw-semibold" id="resultsCount">0</span> properties found
                        </div>
                        <div class="view-toggle">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-secondary active" data-view="grid">
                                    <i class="bi bi-grid-3x3-gap"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary" data-view="list">
                                    <i class="bi bi-list"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Properties Container -->
                    <div class="property-grid row g-4" id="propertyGrid">
                        <!-- Properties will be loaded dynamically -->
                    </div>
                    
                    <!-- Load More Button -->
                    <div class="text-center mt-5">
                        <button class="btn btn-outline-warning btn-lg" id="loadMoreBtn">
                            Load More Properties
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="bi bi-building text-warning me-2"></i>LuxEstate
                    </h5>
                    <p class="text-muted">Your premier destination for luxury real estate worldwide. We connect discerning clients with exceptional properties.</p>
                    <div class="social-links">
                        <a href="#" class="text-warning me-3"><i class="bi bi-facebook fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-twitter fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-instagram fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-linkedin fs-4"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.html" class="text-muted text-decoration-none">Home</a></li>
                        <li><a href="listings.html" class="text-muted text-decoration-none">Properties</a></li>
                        <li><a href="about.html" class="text-muted text-decoration-none">About</a></li>
                        <li><a href="contact.html" class="text-muted text-decoration-none">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Services</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted text-decoration-none">Buy Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Sell Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Rent Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Property Management</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Contact Info</h6>
                    <div class="contact-info">
                        <p class="text-muted mb-2">
                            <i class="bi bi-geo-alt text-warning me-2"></i>
                            123 Luxury Avenue, Premium District, NY 10001
                        </p>
                        <p class="text-muted mb-2">
                            <i class="bi bi-telephone text-warning me-2"></i>
                            +****************
                        </p>
                        <p class="text-muted mb-2">
                            <i class="bi bi-envelope text-warning me-2"></i>
                            <EMAIL>
                        </p>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 LuxEstate. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-muted text-decoration-none me-3">Privacy Policy</a>
                    <a href="#" class="text-muted text-decoration-none">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating Chat Widget -->
    <div class="floating-chat" id="floatingChat">
        <div class="chat-toggle" onclick="toggleChat()">
            <i class="bi bi-chat-dots"></i>
        </div>
        <div class="chat-window" id="chatWindow">
            <div class="chat-header">
                <h6 class="mb-0">Need Help?</h6>
                <button class="btn-close btn-close-white" onclick="toggleChat()"></button>
            </div>
            <div class="chat-body">
                <p class="mb-2">Hi! How can we help you today?</p>
                <div class="quick-actions">
                    <button class="btn btn-sm btn-outline-light mb-2 w-100">Schedule a Viewing</button>
                    <button class="btn btn-sm btn-outline-light mb-2 w-100">Get Property Info</button>
                    <button class="btn btn-sm btn-outline-light w-100">Speak to Agent</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
    
    <!-- AOS JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/property-data.js"></script>
    <script src="assets/js/theme-switcher.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/listings.js"></script>
</body>
</html>
