"""
Message models for the LuxEstate application.
"""

from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Conversation(models.Model):
    """Conversation between users."""
    
    participants = models.ManyToManyField(User, related_name='conversations')
    property = models.ForeignKey('properties.Property', on_delete=models.CASCADE, null=True, blank=True, related_name='conversations')
    subject = models.CharField(max_length=200)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'conversations'
        ordering = ['-updated_at']
        verbose_name = 'Conversation'
        verbose_name_plural = 'Conversations'
    
    def __str__(self):
        return f"Conversation: {self.subject}"
    
    @property
    def last_message(self):
        """Get the last message in this conversation."""
        return self.messages.first()
    
    def get_other_participant(self, user):
        """Get the other participant in a two-person conversation."""
        return self.participants.exclude(id=user.id).first()

class Message(models.Model):
    """Individual message in a conversation."""
    
    MESSAGE_TYPES = (
        ('text', 'Text'),
        ('image', 'Image'),
        ('file', 'File'),
        ('system', 'System'),
    )
    
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages')
    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES, default='text')
    content = models.TextField()
    attachment = models.FileField(upload_to='messages/attachments/', null=True, blank=True)
    
    # Message status
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'messages'
        ordering = ['-created_at']
        verbose_name = 'Message'
        verbose_name_plural = 'Messages'
    
    def __str__(self):
        return f"{self.sender.get_full_name()}: {self.content[:50]}..."
    
    def mark_as_read(self, user=None):
        """Mark message as read."""
        if not self.is_read and (user is None or user != self.sender):
            self.is_read = True
            self.read_at = models.timezone.now()
            self.save(update_fields=['is_read', 'read_at'])

class MessageRead(models.Model):
    """Track which users have read which messages."""
    
    message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='read_by')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    read_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'message_reads'
        unique_together = ('message', 'user')
        verbose_name = 'Message Read'
        verbose_name_plural = 'Message Reads'
    
    def __str__(self):
        return f"{self.user.get_full_name()} read {self.message.id}"

class PropertyInquiry(models.Model):
    """Property inquiry form submissions."""
    
    INQUIRY_TYPES = (
        ('viewing', 'Schedule Viewing'),
        ('info', 'Request Information'),
        ('offer', 'Make Offer'),
        ('general', 'General Inquiry'),
    )
    
    property = models.ForeignKey('properties.Property', on_delete=models.CASCADE, related_name='inquiries')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='property_inquiries', null=True, blank=True)
    
    # Contact information (for anonymous users)
    name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20, blank=True)
    
    # Inquiry details
    inquiry_type = models.CharField(max_length=20, choices=INQUIRY_TYPES, default='general')
    subject = models.CharField(max_length=200)
    message = models.TextField()
    preferred_contact_method = models.CharField(
        max_length=10,
        choices=[('email', 'Email'), ('phone', 'Phone'), ('both', 'Both')],
        default='email'
    )
    
    # Viewing preferences (if applicable)
    preferred_viewing_date = models.DateTimeField(null=True, blank=True)
    viewing_notes = models.TextField(blank=True)
    
    # Status tracking
    is_responded = models.BooleanField(default=False)
    responded_at = models.DateTimeField(null=True, blank=True)
    responded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='responded_inquiries')
    
    # Metadata
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    referrer = models.URLField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'property_inquiries'
        ordering = ['-created_at']
        verbose_name = 'Property Inquiry'
        verbose_name_plural = 'Property Inquiries'
    
    def __str__(self):
        return f"{self.name} - {self.property.title} ({self.inquiry_type})"
    
    def get_contact_name(self):
        """Get the contact name (user's full name or provided name)."""
        return self.user.get_full_name() if self.user else self.name
    
    def get_contact_email(self):
        """Get the contact email (user's email or provided email)."""
        return self.user.email if self.user else self.email

class Notification(models.Model):
    """User notifications."""
    
    NOTIFICATION_TYPES = (
        ('message', 'New Message'),
        ('inquiry', 'Property Inquiry'),
        ('favorite', 'Property Favorited'),
        ('viewing', 'Viewing Scheduled'),
        ('offer', 'Offer Received'),
        ('system', 'System Notification'),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=200)
    message = models.TextField()
    
    # Related objects
    property = models.ForeignKey('properties.Property', on_delete=models.CASCADE, null=True, blank=True)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, null=True, blank=True)
    inquiry = models.ForeignKey(PropertyInquiry, on_delete=models.CASCADE, null=True, blank=True)
    
    # Status
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    
    # Action URL
    action_url = models.URLField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'notifications'
        ordering = ['-created_at']
        verbose_name = 'Notification'
        verbose_name_plural = 'Notifications'
    
    def __str__(self):
        return f"{self.user.get_full_name()} - {self.title}"
    
    def mark_as_read(self):
        """Mark notification as read."""
        if not self.is_read:
            self.is_read = True
            self.read_at = models.timezone.now()
            self.save(update_fields=['is_read', 'read_at'])
