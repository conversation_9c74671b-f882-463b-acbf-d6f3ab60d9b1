"""
Views for the messages app.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q, Count
from django.utils import timezone

from .models import Conversation, Message, PropertyInquiry, Notification
from .serializers import (
    ConversationSerializer, ConversationCreateSerializer, MessageSerializer,
    PropertyInquirySerializer, NotificationSerializer
)

class ConversationViewSet(viewsets.ModelViewSet):
    """ViewSet for conversations."""
    
    serializer_class = ConversationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get conversations for current user."""
        return Conversation.objects.filter(
            participants=self.request.user,
            is_active=True
        ).prefetch_related('participants', 'messages').order_by('-updated_at')
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return ConversationCreateSerializer
        return ConversationSerializer
    
    def perform_create(self, serializer):
        """Add current user to conversation participants."""
        conversation = serializer.save()
        conversation.participants.add(self.request.user)
    
    @action(detail=True, methods=['get'])
    def messages(self, request, pk=None):
        """Get messages for a conversation."""
        conversation = self.get_object()
        messages = conversation.messages.filter(is_deleted=False).order_by('created_at')
        
        # Mark messages as read
        unread_messages = messages.filter(is_read=False).exclude(sender=request.user)
        for message in unread_messages:
            message.mark_as_read(request.user)
        
        serializer = MessageSerializer(messages, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def send_message(self, request, pk=None):
        """Send a message in conversation."""
        conversation = self.get_object()
        
        serializer = MessageSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            message = serializer.save(
                conversation=conversation,
                sender=request.user
            )
            
            # Update conversation timestamp
            conversation.updated_at = timezone.now()
            conversation.save(update_fields=['updated_at'])
            
            return Response(MessageSerializer(message, context={'request': request}).data, 
                          status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, pk=None):
        """Mark all messages in conversation as read."""
        conversation = self.get_object()
        
        unread_messages = conversation.messages.filter(
            is_read=False
        ).exclude(sender=request.user)
        
        for message in unread_messages:
            message.mark_as_read(request.user)
        
        return Response({'status': 'messages marked as read'})

class MessageViewSet(viewsets.ModelViewSet):
    """ViewSet for messages."""
    
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get messages for current user's conversations."""
        return Message.objects.filter(
            conversation__participants=self.request.user,
            is_deleted=False
        ).select_related('sender', 'conversation').order_by('-created_at')
    
    def perform_create(self, serializer):
        """Set sender to current user."""
        serializer.save(sender=self.request.user)
    
    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, pk=None):
        """Mark message as read."""
        message = self.get_object()
        message.mark_as_read(request.user)
        return Response({'status': 'message marked as read'})
    
    @action(detail=True, methods=['delete'])
    def soft_delete(self, request, pk=None):
        """Soft delete message."""
        message = self.get_object()
        
        # Only sender can delete their own messages
        if message.sender != request.user:
            return Response({'error': 'Permission denied'}, status=status.HTTP_403_FORBIDDEN)
        
        message.is_deleted = True
        message.save(update_fields=['is_deleted'])
        
        return Response({'status': 'message deleted'})

class PropertyInquiryViewSet(viewsets.ModelViewSet):
    """ViewSet for property inquiries."""
    
    serializer_class = PropertyInquirySerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    
    def get_queryset(self):
        """Get inquiries based on user permissions."""
        if self.request.user.is_authenticated:
            if self.request.user.is_staff:
                return PropertyInquiry.objects.all().select_related('property', 'user')
            else:
                # Users can see inquiries for their properties or their own inquiries
                return PropertyInquiry.objects.filter(
                    Q(property__owner=self.request.user) |
                    Q(property__agent=self.request.user) |
                    Q(user=self.request.user)
                ).select_related('property', 'user')
        return PropertyInquiry.objects.none()
    
    def perform_create(self, serializer):
        """Create inquiry and send notification."""
        inquiry = serializer.save()
        
        # Create notification for property owner/agent
        property_owner = inquiry.property.owner
        if property_owner:
            Notification.objects.create(
                user=property_owner,
                notification_type='inquiry',
                title=f'New inquiry for {inquiry.property.title}',
                message=f'{inquiry.get_contact_name()} sent an inquiry about your property.',
                property=inquiry.property,
                inquiry=inquiry,
                action_url=f'/dashboard/inquiries/{inquiry.id}/'
            )
    
    @action(detail=True, methods=['post'])
    def respond(self, request, pk=None):
        """Mark inquiry as responded."""
        inquiry = self.get_object()
        
        # Only property owner/agent can respond
        if request.user not in [inquiry.property.owner, inquiry.property.agent]:
            return Response({'error': 'Permission denied'}, status=status.HTTP_403_FORBIDDEN)
        
        inquiry.is_responded = True
        inquiry.responded_at = timezone.now()
        inquiry.responded_by = request.user
        inquiry.save(update_fields=['is_responded', 'responded_at', 'responded_by'])
        
        return Response({'status': 'inquiry marked as responded'})
    
    @action(detail=False, methods=['get'])
    def my_inquiries(self, request):
        """Get current user's inquiries."""
        inquiries = PropertyInquiry.objects.filter(user=request.user).select_related('property')
        serializer = self.get_serializer(inquiries, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def received_inquiries(self, request):
        """Get inquiries for user's properties."""
        inquiries = PropertyInquiry.objects.filter(
            Q(property__owner=request.user) | Q(property__agent=request.user)
        ).select_related('property', 'user')
        
        serializer = self.get_serializer(inquiries, many=True)
        return Response(serializer.data)

class NotificationViewSet(viewsets.ModelViewSet):
    """ViewSet for notifications."""
    
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get notifications for current user."""
        return Notification.objects.filter(user=self.request.user).order_by('-created_at')
    
    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, pk=None):
        """Mark notification as read."""
        notification = self.get_object()
        notification.mark_as_read()
        return Response({'status': 'notification marked as read'})
    
    @action(detail=False, methods=['post'])
    def mark_all_as_read(self, request):
        """Mark all notifications as read."""
        notifications = self.get_queryset().filter(is_read=False)
        for notification in notifications:
            notification.mark_as_read()
        
        return Response({'status': 'all notifications marked as read'})
    
    @action(detail=False, methods=['get'])
    def unread_count(self, request):
        """Get unread notification count."""
        count = self.get_queryset().filter(is_read=False).count()
        return Response({'unread_count': count})
