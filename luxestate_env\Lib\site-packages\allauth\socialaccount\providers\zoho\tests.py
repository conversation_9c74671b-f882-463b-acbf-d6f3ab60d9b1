from allauth.socialaccount.tests import OAuth2TestsMixin
from allauth.tests import MockedResponse, TestCase

from .provider import ZohoProvider


class ZohoTests(OAuth2TestsMixin, TestCase):
    provider_id = ZohoProvider.id

    def get_mocked_response(self):
        return MockedResponse(
            200,
            """
{"First_Name":"<PERSON>","Email":"<EMAIL>",
"Last_Name":"<PERSON><PERSON>","Display_Name":"JDoee","ZUID":1234567}
""",
        )
