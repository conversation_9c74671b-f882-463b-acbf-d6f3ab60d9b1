# Slovenian translations for Django-allauth.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Lev <PERSON> <<EMAIL>>, 2020-06-27
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 04:22-0500\n"
"PO-Revision-Date: 2020-06-27 12:21+0122\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON> <b<PERSON>@mihelac.org>, <PERSON>, "
"<PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n"
"%100==4 ? 2 : 3);\n"
"X-Translated-Using: django-rosetta 0.7.4\n"

#: account/adapter.py:48
msgid "Username can not be used. Please use other username."
msgstr ""
"Uporabniško ime je neveljavno. Prosimo uporabite drugo uporabniško ime."

#: account/adapter.py:54
msgid "Too many failed login attempts. Try again later."
msgstr "Preveliko število neuspelih prijav. Poskusite znova kasneje."

#: account/adapter.py:56
msgid "A user is already registered with this email address."
msgstr "Za ta e-naslov že obstaja registriran uporabnik."

#: account/adapter.py:57
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "Trenutno geslo"

#: account/adapter.py:308
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "Geslo mora vsebovati najmanj {0} znakov. "

#: account/apps.py:9
msgid "Accounts"
msgstr "Računi"

#: account/forms.py:58 account/forms.py:432
msgid "You must type the same password each time."
msgstr "Vnesti je potrebno isto geslo."

#: account/forms.py:90 account/forms.py:397 account/forms.py:522
#: account/forms.py:658
msgid "Password"
msgstr "Geslo"

#: account/forms.py:91
msgid "Remember Me"
msgstr "Zapomni si me"

#: account/forms.py:95
msgid "This account is currently inactive."
msgstr "Račun trenutno ni aktiven."

#: account/forms.py:97
msgid "The email address and/or password you specified are not correct."
msgstr "E-poštni naslov in/ali geslo nista pravilna."

#: account/forms.py:100
msgid "The username and/or password you specified are not correct."
msgstr "Uporabniško ime in/ali geslo nista pravilna."

#: account/forms.py:111 account/forms.py:271 account/forms.py:459
#: account/forms.py:540
msgid "Email address"
msgstr "E-poštni naslov"

#: account/forms.py:115 account/forms.py:316 account/forms.py:456
#: account/forms.py:535
msgid "Email"
msgstr "E-poštni naslov"

#: account/forms.py:118 account/forms.py:121 account/forms.py:261
#: account/forms.py:264
msgid "Username"
msgstr "Uporabniško ime"

#: account/forms.py:131
msgid "Username or email"
msgstr "Uporabniško ime ali e-poštni naslov"

#: account/forms.py:134
msgctxt "field label"
msgid "Login"
msgstr "Prijava"

#: account/forms.py:307
msgid "Email (again)"
msgstr "E-pooštni naslov (ponovno)"

#: account/forms.py:311
msgid "Email address confirmation"
msgstr "Potrditev e-poštni naslova"

#: account/forms.py:319
msgid "Email (optional)"
msgstr "E-poštni naslov (neobvezno)"

#: account/forms.py:368
msgid "You must type the same email each time."
msgstr "Vnesti je potrebno isti e-poštni naslov."

#: account/forms.py:401 account/forms.py:523
msgid "Password (again)"
msgstr "Geslo (ponovno)"

#: account/forms.py:470
msgid "This email address is already associated with this account."
msgstr "E-poštni naslov že pripada vašemu uporabniškemu računu."

#: account/forms.py:472
#, fuzzy, python-format
#| msgid "Your account has no verified email address."
msgid "You cannot add more than %d email addresses."
msgstr "Vaš uporabniški račun nima preverjenega e-poštnega naslova."

#: account/forms.py:503
msgid "Current Password"
msgstr "Trenutno geslo"

#: account/forms.py:505 account/forms.py:607
msgid "New Password"
msgstr "Novo geslo"

#: account/forms.py:506 account/forms.py:608
msgid "New Password (again)"
msgstr "Novo geslo (ponovno)"

#: account/forms.py:514
msgid "Please type your current password."
msgstr "Prosimo vpišite trenutno geslo."

#: account/forms.py:552
msgid "The email address is not assigned to any user account"
msgstr "E-poštni naslov ne pripada nobenemu uporabniškemu računu."

#: account/forms.py:628
msgid "The password reset token was invalid."
msgstr "Žeton za ponastavitev gesla je bil neveljaven."

#: account/models.py:21
msgid "user"
msgstr "uporabnik"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "E-poštni naslov"

#: account/models.py:28
msgid "verified"
msgstr "preverjeno"

#: account/models.py:29
msgid "primary"
msgstr "Primarni"

#: account/models.py:35
msgid "email addresses"
msgstr "E-poštni naslovi"

#: account/models.py:141
msgid "created"
msgstr "ustvarjeno"

#: account/models.py:142
msgid "sent"
msgstr "poslano"

#: account/models.py:143 socialaccount/models.py:62
msgid "key"
msgstr "ključ"

#: account/models.py:148
msgid "email confirmation"
msgstr "E-poštna potrditev"

#: account/models.py:149
msgid "email confirmations"
msgstr "E-poštne potrditve"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:12
msgid "Code"
msgstr ""

#: mfa/forms.py:29
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:30
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Uporabniški račun s tem e-poštnim naslovom že obstaja. Prosimo vpišite se v "
"tobstoječi račun, nato %s račune povežite."

#: socialaccount/adapter.py:136
msgid "Your account has no password set up."
msgstr "Vaš uporabniški račun nima nastavljenega gesla."

#: socialaccount/adapter.py:143
msgid "Your account has no verified email address."
msgstr "Vaš uporabniški račun nima preverjenega e-poštnega naslova."

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "Računi družbenih omrežij."

#: socialaccount/models.py:36 socialaccount/models.py:97
msgid "provider"
msgstr "ponudnik"

#: socialaccount/models.py:45
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "ponudnik"

#: socialaccount/models.py:49
msgid "name"
msgstr "ime"

#: socialaccount/models.py:51
msgid "client id"
msgstr "id številka"

#: socialaccount/models.py:53
msgid "App ID, or consumer key"
msgstr "ID aplikacije ali uporoabniški ključ"

#: socialaccount/models.py:56
msgid "secret key"
msgstr "skrivni ključ"

#: socialaccount/models.py:59
msgid "API secret, client secret, or consumer secret"
msgstr "API skrivnost, skrivnost klienta ali uporabniška skrivnost"

#: socialaccount/models.py:62
msgid "Key"
msgstr "Ključ"

#: socialaccount/models.py:81
msgid "social application"
msgstr "družbena aplikacija"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "družbene aplikacije"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "zadnja prijava"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "datum pridružitve"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "dodatni podatki"

#: socialaccount/models.py:125
msgid "social account"
msgstr "uporabniški račun družbenih omerižij"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "uporabniški računi družbenih omerižij"

#: socialaccount/models.py:160
msgid "token"
msgstr "žeton"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) ali žeton za dostop (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "žeton skrivnost"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) ali žeton za osvežitev (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "veljavnost poteče"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "žeton družebnih omrežij"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "žetoni družbenih omrežij"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "Nevelljavni podatki profila"

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Neveljaven odgovor ob pridobivanju žetona za zahtevo od \"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Neveljaven odgovor ob pridobivanju žetona za dostop od \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Za \"%s\" ni shranjenega žetona za zahtevo."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Za \"%s\" ni shranjenega žetona za dostop."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Ni dostopa do zasebnega vira na \"%s\"."

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Neveljaven odgovor ob pridobivanju žetona za zahtevo od \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:8
msgid "Account Inactive"
msgstr "Neaktiven račun"

#: templates/account/account_inactive.html:10
msgid "This account is inactive."
msgstr "Račun ni aktiven."

#: templates/account/base.html:16
msgid "Messages:"
msgstr ""

#: templates/account/base.html:26
msgid "Menu:"
msgstr ""

#: templates/account/base.html:29 templates/account/email_change.html:31
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "E-poštni naslov"

#: templates/account/base.html:30 templates/account/logout.html:5
#: templates/account/logout.html:8 templates/account/logout.html:17
msgid "Sign Out"
msgstr "Odjava"

#: templates/account/base.html:32 templates/account/login.html:6
#: templates/account/login.html:10 templates/account/login.html:43
#: templates/mfa/authenticate.html:4 templates/mfa/authenticate.html:16
#: templates/socialaccount/login.html:4
msgid "Sign In"
msgstr "Prijava"

#: templates/account/base.html:33 templates/account/signup.html:8
#: templates/account/signup.html:18 templates/socialaccount/signup.html:8
#: templates/socialaccount/signup.html:19
msgid "Sign Up"
msgstr "Ustvari račun"

#: templates/account/email.html:5 templates/account/email.html:8
msgid "Email Addresses"
msgstr "E-poštni naslovi"

#: templates/account/email.html:10
msgid "The following email addresses are associated with your account:"
msgstr "E-poštni naslovi, ki pripadajo vašemu uporabniškemu računu:"

#: templates/account/email.html:24
msgid "Verified"
msgstr "Preverjeni"

#: templates/account/email.html:26
msgid "Unverified"
msgstr "Nepreverjeni"

#: templates/account/email.html:28
msgid "Primary"
msgstr "Primarni"

#: templates/account/email.html:34
msgid "Make Primary"
msgstr "Spremeni v primarni"

#: templates/account/email.html:35 templates/account/email_change.html:21
msgid "Re-send Verification"
msgstr "Ponovno pošlji verifikacijo"

#: templates/account/email.html:36 templates/socialaccount/connections.html:35
msgid "Remove"
msgstr "Odstrani"

#: templates/account/email.html:47
msgid "Add Email Address"
msgstr "Dodaj e-poštni naslov"

#: templates/account/email.html:52
msgid "Add Email"
msgstr "Dodaj e-pošto"

#: templates/account/email.html:62
msgid "Do you really want to remove the selected email address?"
msgstr "Želite odstraniti izbran e-poštni nalsov?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, python-format
msgid "Hello from %(site_name)s!"
msgstr "Pozdravljeni iz %(site_name)s"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Hvala, ker uporabljate %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "You're receiving this e-mail because user %(user_display)s has given your "
#| "e-mail address to register an account on %(site_domain)s.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"To sporočilo ste prejeli, ker je uporabnik %(user_display)s povezal vaš e-"
"poštni naslov s svojim uporabniškim računom na %(site_domain)s.\n"
"\n"
"Za potrditev sledite povezavi: %(activate_url)s"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Prosimo, potrdite svoj e-poštni naslov."

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"To sporočilo ste prejeli, ker ste zahtevali ponastavitev gesla za vaš račun."
"To sporočilo lahko zavržete, če niste poslali zahtevka za spremembo gesla. "
"Za ponastavitev gesla, sledie spodnji povezavi:"

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "V primeru, da ste pozabili, vaše uporabniško ime je: %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "E-poštni naslov za ponastavitev gesla"

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Pozdravljeni od %(site_name)s!\n"
"To sporočilo ste prejeli, ker ste zahtevali ponastavitev gesla na "
"%(site_name)s!\n"
"To sporočilo lahko zavržete, če niste poslali zahtevka za spremembo gesla. "
"Za ponastavitev gelsa, sledie spodnji pvezavi:"

#: templates/account/email_change.html:4 templates/account/email_change.html:7
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "E-poštni naslovi"

#: templates/account/email_change.html:11
#, fuzzy
#| msgid "The following email addresses are associated with your account:"
msgid "The following email address is associated with your account:"
msgstr "E-poštni naslovi, ki pripadajo vašemu uporabniškemu računu:"

#: templates/account/email_change.html:16
msgid "Your email address is still pending verification:"
msgstr ""

#: templates/account/email_change.html:27
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Change Email Address"
msgstr "Potrdite e-poštni naslov"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Potrdite e-poštni naslov"

#: templates/account/email_confirm.html:17
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Prosimo, potrdite, da je <a href=\"mailto:%(email)s\">%(email)s</a> e-poštni "
"naslov uporabnika %(user_display)s."

#: templates/account/email_confirm.html:21
#: templates/account/reauthenticate.html:19
msgid "Confirm"
msgstr "Potrdi"

#: templates/account/email_confirm.html:24
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Obstoječi račun drugega ponudnika, je povezan z obstoječim računom."

#: templates/account/email_confirm.html:31
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a href="
#| "\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Povezava za potrditev je neveljavna, ali pa se je iztekla. Prosimo, <a href="
"\"%(email_url)s\">pošljite nov zahtevek za potrditev.</a>."

#: templates/account/login.html:15
#, python-format
msgid ""
"Please sign in with one\n"
"of your existing third party accounts. Or, <a href=\"%(signup_url)s\">sign "
"up</a>\n"
"for a %(site_name)s account and sign in below:"
msgstr ""
"Prosimo, vpišite se z enim od vaših\n"
"obstoječih računov, ali pa <a href=\"%(signup_url)s\">ustvarite nov račun</"
"a>\n"
"na %(site_name)s in se vpišite spodaj:"

#: templates/account/login.html:25
msgid "or"
msgstr "ali"

#: templates/account/login.html:32
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"<a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Če še nimate uporabniškega računa, ga ustvarite\n"
"<a href=\"%(signup_url)s\">tukaj</a>."

#: templates/account/login.html:42 templates/account/password_change.html:14
msgid "Forgot Password?"
msgstr "Ste pozabili geslo?"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Ali ste prepričani, da se želite odjaviti?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Primarnega e-poštnega naslova (%(email)s) ni mogoče odstraniti."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Na %(email)s je bilo poslano potrditveno sporočilo."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "%(email)s je potrjen."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "E-poštni naslov %(email)s je bil odstranjen."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Uspešno ste se prijavili kot %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Odjavili ste se."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Geslo je uspešno zamenjano."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Geslo je uspešno nastavljeno."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Primarni e-poštni naslov je nastavljen."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr ""

#: templates/account/password_change.html:5
#: templates/account/password_change.html:8
#: templates/account/password_change.html:13
#: templates/account/password_reset_from_key.html:4
#: templates/account/password_reset_from_key.html:7
#: templates/account/password_reset_from_key_done.html:4
#: templates/account/password_reset_from_key_done.html:7
msgid "Change Password"
msgstr "Sprememba gesla"

#: templates/account/password_reset.html:6
#: templates/account/password_reset.html:10
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:9
msgid "Password Reset"
msgstr "Ponastavitev gesla"

#: templates/account/password_reset.html:15
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Vnesite vaš e-potni naslov, na katerega boste prejeli povezavo za "
"ponastavitev gesla."

#: templates/account/password_reset.html:20
msgid "Reset My Password"
msgstr "Ponastavi geslo"

#: templates/account/password_reset.html:23
msgid "Please contact us if you have any trouble resetting your password."
msgstr "V primeru težav pri ponastavljanju gesla, nas kontaktirajte."

#: templates/account/password_reset_done.html:15
#, fuzzy
#| msgid ""
#| "We have sent you an e-mail. If you have not received it please check your "
#| "spam folder. Otherwise contact us if you do not receive it in a few "
#| "minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Poslali smo vam e-poštno sporočilo s povezavo za \n"
"potrditev. Če sporočila ne boste prejeli \n"
"v nekaj minutah, nas kontaktirajte."

#: templates/account/password_reset_from_key.html:7
msgid "Bad Token"
msgstr "Napačni žeton"

#: templates/account/password_reset_from_key.html:11
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Povezava za ponastavitev gesla je neveljavna oz. je bila že uporoabljena. "
"Prosimo, zahtevajte <a href=\"%(passwd_reset_url)s\">novo povezavo</a> za "
"ponastavitev."

#: templates/account/password_reset_from_key.html:16
msgid "change password"
msgstr "Sprememba gesla"

#: templates/account/password_reset_from_key_done.html:8
msgid "Your password is now changed."
msgstr "Geslo je spremenjeno."

#: templates/account/password_set.html:5 templates/account/password_set.html:8
#: templates/account/password_set.html:13
msgid "Set Password"
msgstr "Nastavi geslo"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "Potrdite e-poštni naslov"

#: templates/account/reauthenticate.html:11
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""

#: templates/account/signup.html:5 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Ustvari račun"

#: templates/account/signup.html:10
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr ""
"Že imate uporabniški račun? <a href=\"%(login_url)s\">Prijavite se</a>. "

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:8
msgid "Sign Up Closed"
msgstr "Registracija ni mogoča"

#: templates/account/signup_closed.html:10
msgid "We are sorry, but the sign up is currently closed."
msgstr "Registracija trenutno ni mogoča"

#: templates/account/snippets/already_logged_in.html:5
msgid "Note"
msgstr "Opomba"

#: templates/account/snippets/already_logged_in.html:5
#, python-format
msgid "you are already logged in as %(user_display)s."
msgstr "Prijavljeni ste kot %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Opozorilo:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Trenutno nimate natavljlenega e-poštnega naslova. Priporočamo vam, da dodate "
"e-poštni naslov, za prejemanje obvestil, zahtevkv za ponastavitev gesla, ipd."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:8
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:8
msgid "Verify Your Email Address"
msgstr "Potrdite e-poštni naslov."

#: templates/account/verification_sent.html:10
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. If you do not see the verification e-mail "
#| "in your main inbox, check your spam folder. Please contact us if you do "
#| "not receive the verification e-mail within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Na vaš e-poštni naslov je bil poslano sporočilo za potrditev. Sledite "
"povezavi za zaključek registracije. Če sporočila ne boste prejeli v nekaj "
"minutah, nas kontaktirajte."

#: templates/account/verified_email_required.html:12
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Za dostop morate potrditi svojo identiteto. \n"
"Prosimo, da potrdite navedeni e-poštni naslov."

#: templates/account/verified_email_required.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside that e-mail. If you do not "
#| "see the verification e-mail in your main inbox, check your spam folder. "
#| "Otherwise\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Poslali smo vam e-poštno sporočilo s povezavo za \n"
"potrditev. Če sporočila ne boste prejeli \n"
"v nekaj minutah, nas kontaktirajte."

#: templates/account/verified_email_required.html:20
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Opomba:</strong> <a href=\"%(email_url)s\">e-poštni naslov</a> lahko "
"spremenite."

#: templates/mfa/authenticate.html:7 templates/mfa/index.html:4
#: templates/mfa/index.html:7
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:9
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:9 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:11
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/deactivate_form.html:11
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:18
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:21 templates/mfa/totp/activate_form.html:16
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:4
#: templates/mfa/recovery_codes/index.html:4
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:30 templates/mfa/recovery_codes/index.html:6
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: templates/mfa/index.html:34
msgid "View codes"
msgstr ""

#: templates/mfa/index.html:37 templates/mfa/recovery_codes/index.html:16
msgid "Download codes"
msgstr ""

#: templates/mfa/index.html:40 templates/mfa/recovery_codes/index.html:20
msgid "Generate new codes"
msgstr ""

#: templates/mfa/index.html:44
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:6
msgid ""
"You are about to generate a new set of recovery codes for your account. This "
"action will invalidate your existing codes. Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "Generate"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:9
#, fuzzy
#| msgid "token secret"
msgid "Authenticator secret"
msgstr "žeton skrivnost"

#: templates/mfa/totp/deactivate_form.html:4
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:6
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/openid/login.html:9
msgid "OpenID Sign In"
msgstr "Prijava z OpenID"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:8
msgid "Social Network Login Failure"
msgstr "Prijava z računom drugega ponudnika ni uspela."

#: templates/socialaccount/authentication_error.html:10
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr ""
"Prišo je do napake ob poskusu prijave z vašim obstoječim računom drugega "
"ponudnuka."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:8
msgid "Account Connections"
msgstr "Povezave računov"

#: templates/socialaccount/connections.html:11
msgid ""
"You can sign in to your account using any of the following third party "
"accounts:"
msgstr "Vpišete se lahko s pomčjo vaših obstoječih uoporabniških računov:"

#: templates/socialaccount/connections.html:43
msgid ""
"You currently have no social network accounts connected to this account."
msgstr ""
"Trenutno nimate povezanega nobenega uporabniškega računa drugega ponudnika."

#: templates/socialaccount/connections.html:46
msgid "Add a 3rd Party Account"
msgstr "Dodaj obstoječi račun drugega ponudnika"

#: templates/socialaccount/login.html:8
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:10
#, python-format
msgid "You are about to connect a new third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:12
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:14
#, python-format
msgid "You are about to sign in using a third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:19
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Prijava je bila prekinjena"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"Odločili ste se za prekinitev prijave v našo storitev s pomočjo obstoječega "
"računa. Če gre za napako, se lahko prijavite <a href=\"%(login_url)s"
"\">tukaj</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "Obstoječi račun je povezan."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "Obstoječi račun drugega ponudnika, je povezan z obstoječim računom."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "Obstoječi uporabniški račun je razvezan."

#: templates/socialaccount/signup.html:10
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Uporabili boste svoj obstoječi %(provider_name)s račun, za vpis v\n"
"%(site_name)s. Prosimo izpolnite spodnji obrazec:"

#~ msgid "This email address is already associated with another account."
#~ msgstr "E-poštni naslov že pripada drugemu uporabniškemu računu."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Povezava za ponastavitev gesla je bila poslana. Če je ne boste prejeli v "
#~ "nekaj minutah, nas kontaktirajte."
