"""
Filters for the properties app.
"""

import django_filters
from django.db.models import Q
from .models import Property, PropertyCategory

class PropertyFilter(django_filters.FilterSet):
    """Filter for Property model."""
    
    # Price filters
    min_price = django_filters.NumberFilter(field_name='price', lookup_expr='gte')
    max_price = django_filters.NumberFilter(field_name='price', lookup_expr='lte')
    
    # Rent price filters
    min_rent_price = django_filters.NumberFilter(field_name='rent_price', lookup_expr='gte')
    max_rent_price = django_filters.NumberFilter(field_name='rent_price', lookup_expr='lte')
    
    # Bedroom filters
    min_bedrooms = django_filters.NumberFilter(field_name='bedrooms', lookup_expr='gte')
    max_bedrooms = django_filters.NumberFilter(field_name='bedrooms', lookup_expr='lte')
    
    # Bathroom filters
    min_bathrooms = django_filters.NumberFilter(field_name='bathrooms', lookup_expr='gte')
    max_bathrooms = django_filters.NumberFilter(field_name='bathrooms', lookup_expr='lte')
    
    # Area filters
    min_area = django_filters.NumberFilter(field_name='area', lookup_expr='gte')
    max_area = django_filters.NumberFilter(field_name='area', lookup_expr='lte')
    
    # Location filters
    city = django_filters.CharFilter(field_name='city', lookup_expr='icontains')
    state = django_filters.CharFilter(field_name='state', lookup_expr='icontains')
    
    # Feature filters
    features = django_filters.CharFilter(method='filter_features')
    amenities = django_filters.CharFilter(method='filter_amenities')
    
    # Date filters
    created_after = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte')
    created_before = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte')
    
    class Meta:
        model = Property
        fields = {
            'listing_type': ['exact'],
            'property_type': ['exact'],
            'status': ['exact'],
            'is_featured': ['exact'],
            'is_premium': ['exact'],
            'owner': ['exact'],
            'agent': ['exact'],
            'category': ['exact'],
        }
    
    def filter_features(self, queryset, name, value):
        """Filter by features."""
        if value:
            features_list = [f.strip() for f in value.split(',')]
            for feature in features_list:
                queryset = queryset.filter(features__contains=[feature])
        return queryset
    
    def filter_amenities(self, queryset, name, value):
        """Filter by amenities."""
        if value:
            amenities_list = [a.strip() for a in value.split(',')]
            for amenity in amenities_list:
                queryset = queryset.filter(amenities__contains=[amenity])
        return queryset
