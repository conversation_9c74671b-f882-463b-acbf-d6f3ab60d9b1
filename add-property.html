<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Property - LuxEstate</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    
    <!-- AOS CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <style>
        /* Add Property Page Specific Styles */
        .form-section {
            background: var(--bs-body-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid var(--bs-border-color);
        }
        
        .form-section h4 {
            color: var(--luxury-gold);
            border-bottom: 2px solid var(--luxury-gold);
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }
        
        .image-upload-area {
            border: 2px dashed var(--bs-border-color);
            border-radius: var(--border-radius);
            padding: 3rem 2rem;
            text-align: center;
            transition: var(--transition);
            cursor: pointer;
            background: var(--bs-secondary-bg);
        }
        
        .image-upload-area:hover {
            border-color: var(--luxury-gold);
            background: var(--bs-tertiary-bg);
        }
        
        .image-upload-area.dragover {
            border-color: var(--luxury-gold);
            background: rgba(255, 193, 7, 0.1);
        }
        
        .uploaded-images {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .uploaded-image {
            position: relative;
            border-radius: var(--border-radius);
            overflow: hidden;
            aspect-ratio: 1;
        }
        
        .uploaded-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .remove-image {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(220, 53, 69, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 0.8rem;
        }
        
        .feature-checkbox {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .feature-checkbox:hover {
            background: var(--bs-secondary-bg);
        }
        
        .feature-checkbox input[type="checkbox"]:checked + label {
            color: var(--luxury-gold);
            font-weight: 600;
        }
        
        .progress-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
        }
        
        .progress-step {
            flex: 1;
            text-align: center;
            position: relative;
        }
        
        .progress-step::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 50%;
            right: -50%;
            height: 2px;
            background: var(--bs-border-color);
            z-index: 1;
        }
        
        .progress-step:last-child::before {
            display: none;
        }
        
        .progress-step.active::before {
            background: var(--luxury-gold);
        }
        
        .progress-step-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--bs-border-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            position: relative;
            z-index: 2;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .progress-step.active .progress-step-circle {
            background: var(--luxury-gold);
        }
        
        .progress-step.completed .progress-step-circle {
            background: #28a745;
        }
        
        .progress-step-label {
            font-size: 0.9rem;
            color: var(--bs-secondary-color);
        }
        
        .progress-step.active .progress-step-label {
            color: var(--luxury-gold);
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top luxury-nav">
        <div class="container">
            <a class="navbar-brand fw-bold fs-3" href="index.html">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </a>
            
            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Desktop Navigation -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="listings.html">Properties</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.html">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">Contact</a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <!-- Theme Switcher -->
                    <div class="dropdown me-3">
                        <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-palette"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item theme-option" href="#" data-theme="light">
                                <i class="bi bi-sun"></i> Light
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="dark">
                                <i class="bi bi-moon"></i> Dark
                            </a></li>
                            <li><a class="dropdown-item theme-option" href="#" data-theme="luxury">
                                <i class="bi bi-gem"></i> Luxury
                            </a></li>
                        </ul>
                    </div>
                    
                    <a href="favorites.html" class="btn btn-outline-light btn-sm me-2">
                        <i class="bi bi-heart"></i> <span class="d-none d-md-inline">Favorites</span>
                    </a>
                    <a href="login.html" class="btn btn-warning btn-sm me-2">Login</a>
                    <a href="add-property.html" class="btn btn-outline-warning btn-sm">
                        <i class="bi bi-plus-circle"></i> <span class="d-none d-md-inline">Add Property</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Navigation Offcanvas -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="mobileNav">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">
                <i class="bi bi-building text-warning me-2"></i>LuxEstate
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="index.html">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="listings.html">Properties</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="about.html">About</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="blog.html">Blog</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="contact.html">Contact</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="favorites.html">Favorites</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.html">Dashboard</a>
                </li>
            </ul>
            <hr>
            <div class="d-grid gap-2">
                <a href="login.html" class="btn btn-warning">Login</a>
                <a href="add-property.html" class="btn btn-outline-warning">Add Property</a>
            </div>
        </div>
    </div>

    <!-- Page Header -->
    <section class="py-5 mt-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                    <h1 class="display-5 fw-bold mb-3">Add Your Property</h1>
                    <p class="lead text-muted">List your luxury property with LuxEstate and reach qualified buyers worldwide</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Add Property Form -->
    <section class="py-5">
        <div class="container">
            <!-- Progress Indicator -->
            <div class="progress-indicator" data-aos="fade-up">
                <div class="progress-step active">
                    <div class="progress-step-circle">1</div>
                    <div class="progress-step-label">Basic Info</div>
                </div>
                <div class="progress-step">
                    <div class="progress-step-circle">2</div>
                    <div class="progress-step-label">Details</div>
                </div>
                <div class="progress-step">
                    <div class="progress-step-circle">3</div>
                    <div class="progress-step-label">Images</div>
                </div>
                <div class="progress-step">
                    <div class="progress-step-circle">4</div>
                    <div class="progress-step-label">Review</div>
                </div>
            </div>
            
            <form class="needs-validation" novalidate id="addPropertyForm" data-aos="fade-up" data-aos-delay="200">
                <!-- Basic Information Section -->
                <div class="form-section">
                    <h4><i class="bi bi-info-circle me-2"></i>Basic Information</h4>
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="propertyTitle" class="form-label">Property Title *</label>
                            <input type="text" class="form-control" id="propertyTitle" required placeholder="e.g., Luxury Penthouse Manhattan">
                            <div class="invalid-feedback">Please enter a property title.</div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="propertyType" class="form-label">Property Type *</label>
                            <select class="form-select" id="propertyType" required>
                                <option value="">Select Type</option>
                                <option value="house">House</option>
                                <option value="apartment">Apartment</option>
                                <option value="villa">Villa</option>
                                <option value="penthouse">Penthouse</option>
                                <option value="mansion">Mansion</option>
                                <option value="townhouse">Townhouse</option>
                                <option value="loft">Loft</option>
                                <option value="chalet">Chalet</option>
                            </select>
                            <div class="invalid-feedback">Please select a property type.</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="propertyPrice" class="form-label">Price (USD) *</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" id="propertyPrice" required min="1" placeholder="1000000">
                                <div class="invalid-feedback">Please enter a valid price.</div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="propertyArea" class="form-label">Area (sq ft) *</label>
                            <input type="number" class="form-control" id="propertyArea" required min="1" placeholder="2500">
                            <div class="invalid-feedback">Please enter the property area.</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="propertyLocation" class="form-label">Location *</label>
                        <input type="text" class="form-control" id="propertyLocation" required placeholder="e.g., Upper East Side, New York, NY">
                        <div class="invalid-feedback">Please enter the property location.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="propertyDescription" class="form-label">Description *</label>
                        <textarea class="form-control" id="propertyDescription" rows="4" required placeholder="Describe your property in detail..."></textarea>
                        <div class="invalid-feedback">Please enter a property description.</div>
                    </div>
                </div>
                
                <!-- Property Details Section -->
                <div class="form-section">
                    <h4><i class="bi bi-house me-2"></i>Property Details</h4>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="bedrooms" class="form-label">Bedrooms *</label>
                            <select class="form-select" id="bedrooms" required>
                                <option value="">Select</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                                <option value="6">6</option>
                                <option value="7">7+</option>
                            </select>
                            <div class="invalid-feedback">Please select number of bedrooms.</div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="bathrooms" class="form-label">Bathrooms *</label>
                            <select class="form-select" id="bathrooms" required>
                                <option value="">Select</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                                <option value="6">6+</option>
                            </select>
                            <div class="invalid-feedback">Please select number of bathrooms.</div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="yearBuilt" class="form-label">Year Built</label>
                            <input type="number" class="form-control" id="yearBuilt" min="1800" max="2024" placeholder="2020">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Property Features</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="feature-checkbox">
                                    <input type="checkbox" id="pool" name="features" value="pool">
                                    <label for="pool">Swimming Pool</label>
                                </div>
                                <div class="feature-checkbox">
                                    <input type="checkbox" id="garage" name="features" value="garage">
                                    <label for="garage">Garage</label>
                                </div>
                                <div class="feature-checkbox">
                                    <input type="checkbox" id="garden" name="features" value="garden">
                                    <label for="garden">Garden</label>
                                </div>
                                <div class="feature-checkbox">
                                    <input type="checkbox" id="balcony" name="features" value="balcony">
                                    <label for="balcony">Balcony</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-checkbox">
                                    <input type="checkbox" id="fireplace" name="features" value="fireplace">
                                    <label for="fireplace">Fireplace</label>
                                </div>
                                <div class="feature-checkbox">
                                    <input type="checkbox" id="gym" name="features" value="gym">
                                    <label for="gym">Gym</label>
                                </div>
                                <div class="feature-checkbox">
                                    <input type="checkbox" id="spa" name="features" value="spa">
                                    <label for="spa">Spa</label>
                                </div>
                                <div class="feature-checkbox">
                                    <input type="checkbox" id="wine-cellar" name="features" value="wine-cellar">
                                    <label for="wine-cellar">Wine Cellar</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-checkbox">
                                    <input type="checkbox" id="security" name="features" value="security">
                                    <label for="security">Security System</label>
                                </div>
                                <div class="feature-checkbox">
                                    <input type="checkbox" id="elevator" name="features" value="elevator">
                                    <label for="elevator">Elevator</label>
                                </div>
                                <div class="feature-checkbox">
                                    <input type="checkbox" id="smart-home" name="features" value="smart-home">
                                    <label for="smart-home">Smart Home</label>
                                </div>
                                <div class="feature-checkbox">
                                    <input type="checkbox" id="ocean-view" name="features" value="ocean-view">
                                    <label for="ocean-view">Ocean View</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Images Section -->
                <div class="form-section">
                    <h4><i class="bi bi-images me-2"></i>Property Images</h4>
                    
                    <div class="image-upload-area" id="imageUploadArea">
                        <i class="bi bi-cloud-upload display-4 text-muted mb-3"></i>
                        <h5>Drag & Drop Images Here</h5>
                        <p class="text-muted mb-3">or click to browse files</p>
                        <button type="button" class="btn btn-outline-warning">Choose Files</button>
                        <input type="file" id="imageInput" multiple accept="image/*" style="display: none;">
                        <small class="text-muted d-block mt-2">Maximum 10 images, 5MB each</small>
                    </div>
                    
                    <div class="uploaded-images" id="uploadedImages">
                        <!-- Uploaded images will appear here -->
                    </div>
                </div>
                
                <!-- Contact Information Section -->
                <div class="form-section">
                    <h4><i class="bi bi-person me-2"></i>Contact Information</h4>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="contactName" class="form-label">Your Name *</label>
                            <input type="text" class="form-control" id="contactName" required>
                            <div class="invalid-feedback">Please enter your name.</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="contactEmail" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="contactEmail" required>
                            <div class="invalid-feedback">Please enter a valid email address.</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="contactPhone" class="form-label">Phone Number *</label>
                            <input type="tel" class="form-control" id="contactPhone" required>
                            <div class="invalid-feedback">Please enter your phone number.</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="preferredContact" class="form-label">Preferred Contact Method</label>
                            <select class="form-select" id="preferredContact">
                                <option value="email">Email</option>
                                <option value="phone">Phone</option>
                                <option value="both">Both</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Terms and Submit -->
                <div class="form-section">
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                        <label class="form-check-label" for="agreeTerms">
                            I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                        </label>
                        <div class="invalid-feedback">You must agree to the terms and conditions.</div>
                    </div>
                    
                    <div class="d-flex gap-3 justify-content-end">
                        <button type="button" class="btn btn-outline-secondary btn-lg">Save as Draft</button>
                        <button type="submit" class="btn btn-warning btn-lg">
                            <i class="bi bi-check-circle me-2"></i>Submit Property
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="bi bi-building text-warning me-2"></i>LuxEstate
                    </h5>
                    <p class="text-muted">Your premier destination for luxury real estate worldwide. We connect discerning clients with exceptional properties.</p>
                    <div class="social-links">
                        <a href="#" class="text-warning me-3"><i class="bi bi-facebook fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-twitter fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-instagram fs-4"></i></a>
                        <a href="#" class="text-warning me-3"><i class="bi bi-linkedin fs-4"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.html" class="text-muted text-decoration-none">Home</a></li>
                        <li><a href="listings.html" class="text-muted text-decoration-none">Properties</a></li>
                        <li><a href="about.html" class="text-muted text-decoration-none">About</a></li>
                        <li><a href="contact.html" class="text-muted text-decoration-none">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Services</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted text-decoration-none">Buy Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Sell Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Rent Property</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Property Management</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Contact Info</h6>
                    <div class="contact-info">
                        <p class="text-muted mb-2">
                            <i class="bi bi-geo-alt text-warning me-2"></i>
                            123 Luxury Avenue, Premium District, NY 10001
                        </p>
                        <p class="text-muted mb-2">
                            <i class="bi bi-telephone text-warning me-2"></i>
                            +****************
                        </p>
                        <p class="text-muted mb-2">
                            <i class="bi bi-envelope text-warning me-2"></i>
                            <EMAIL>
                        </p>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 LuxEstate. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-muted text-decoration-none me-3">Privacy Policy</a>
                    <a href="#" class="text-muted text-decoration-none">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating Chat Widget -->
    <div class="floating-chat" id="floatingChat">
        <div class="chat-toggle" onclick="toggleChat()">
            <i class="bi bi-chat-dots"></i>
        </div>
        <div class="chat-window" id="chatWindow">
            <div class="chat-header">
                <h6 class="mb-0">Need Help?</h6>
                <button class="btn-close btn-close-white" onclick="toggleChat()"></button>
            </div>
            <div class="chat-body">
                <p class="mb-2">Hi! How can we help you today?</p>
                <div class="quick-actions">
                    <button class="btn btn-sm btn-outline-light mb-2 w-100">Property Listing Help</button>
                    <button class="btn btn-sm btn-outline-light mb-2 w-100">Pricing Guidance</button>
                    <button class="btn btn-sm btn-outline-light w-100">Speak to Agent</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- AOS JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/theme-switcher.js"></script>
    <script src="assets/js/add-property.js"></script>
</body>
</html>
