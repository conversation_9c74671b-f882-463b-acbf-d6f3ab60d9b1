"""
Analytics models for the LuxEstate application.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

User = get_user_model()

class PropertyAnalytics(models.Model):
    """Daily analytics for properties."""
    
    property = models.ForeignKey('properties.Property', on_delete=models.CASCADE, related_name='analytics')
    date = models.DateField()
    
    # View metrics
    views = models.PositiveIntegerField(default=0)
    unique_views = models.PositiveIntegerField(default=0)
    
    # Engagement metrics
    inquiries = models.PositiveIntegerField(default=0)
    favorites = models.PositiveIntegerField(default=0)
    shares = models.PositiveIntegerField(default=0)
    
    # Conversion metrics
    viewing_requests = models.PositiveIntegerField(default=0)
    offers_received = models.PositiveIntegerField(default=0)
    
    # Time metrics
    average_time_on_page = models.DurationField(null=True, blank=True)
    bounce_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'property_analytics'
        unique_together = ('property', 'date')
        ordering = ['-date']
        verbose_name = 'Property Analytics'
        verbose_name_plural = 'Property Analytics'
    
    def __str__(self):
        return f"{self.property.title} - {self.date}"

class UserAnalytics(models.Model):
    """Daily analytics for users (agents/sellers)."""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='analytics')
    date = models.DateField()
    
    # Property metrics
    properties_viewed = models.PositiveIntegerField(default=0)
    properties_favorited = models.PositiveIntegerField(default=0)
    properties_shared = models.PositiveIntegerField(default=0)
    
    # Engagement metrics
    messages_sent = models.PositiveIntegerField(default=0)
    messages_received = models.PositiveIntegerField(default=0)
    inquiries_sent = models.PositiveIntegerField(default=0)
    inquiries_received = models.PositiveIntegerField(default=0)
    
    # Session metrics
    sessions = models.PositiveIntegerField(default=0)
    session_duration = models.DurationField(null=True, blank=True)
    pages_viewed = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'user_analytics'
        unique_together = ('user', 'date')
        ordering = ['-date']
        verbose_name = 'User Analytics'
        verbose_name_plural = 'User Analytics'
    
    def __str__(self):
        return f"{self.user.get_full_name()} - {self.date}"

class SearchAnalytics(models.Model):
    """Analytics for search queries."""
    
    query = models.CharField(max_length=255)
    filters = models.JSONField(default=dict)
    results_count = models.PositiveIntegerField(default=0)
    
    # User information
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    
    # Location data
    city = models.CharField(max_length=100, blank=True)
    country = models.CharField(max_length=100, blank=True)
    
    # Engagement
    clicked_results = models.PositiveIntegerField(default=0)
    time_spent = models.DurationField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'search_analytics'
        ordering = ['-created_at']
        verbose_name = 'Search Analytics'
        verbose_name_plural = 'Search Analytics'
        indexes = [
            models.Index(fields=['query']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Search: {self.query} ({self.results_count} results)"

class MarketTrends(models.Model):
    """Market trends and statistics."""
    
    TREND_TYPES = (
        ('price', 'Price Trend'),
        ('inventory', 'Inventory Trend'),
        ('demand', 'Demand Trend'),
        ('location', 'Location Trend'),
    )
    
    trend_type = models.CharField(max_length=20, choices=TREND_TYPES)
    location = models.CharField(max_length=100)  # City, State
    property_type = models.CharField(max_length=20, blank=True)
    
    # Trend data
    current_value = models.DecimalField(max_digits=15, decimal_places=2)
    previous_value = models.DecimalField(max_digits=15, decimal_places=2)
    percentage_change = models.DecimalField(max_digits=5, decimal_places=2)
    
    # Time period
    period_start = models.DateField()
    period_end = models.DateField()
    
    # Additional data
    data_points = models.JSONField(default=dict)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'market_trends'
        ordering = ['-created_at']
        verbose_name = 'Market Trend'
        verbose_name_plural = 'Market Trends'
    
    def __str__(self):
        return f"{self.get_trend_type_display()} - {self.location}"

class PerformanceMetrics(models.Model):
    """Overall performance metrics for the platform."""
    
    date = models.DateField(unique=True)
    
    # User metrics
    total_users = models.PositiveIntegerField(default=0)
    new_users = models.PositiveIntegerField(default=0)
    active_users = models.PositiveIntegerField(default=0)
    
    # Property metrics
    total_properties = models.PositiveIntegerField(default=0)
    new_properties = models.PositiveIntegerField(default=0)
    sold_properties = models.PositiveIntegerField(default=0)
    rented_properties = models.PositiveIntegerField(default=0)
    
    # Engagement metrics
    total_views = models.PositiveIntegerField(default=0)
    total_inquiries = models.PositiveIntegerField(default=0)
    total_messages = models.PositiveIntegerField(default=0)
    total_searches = models.PositiveIntegerField(default=0)
    
    # Business metrics
    total_leads = models.PositiveIntegerField(default=0)
    converted_leads = models.PositiveIntegerField(default=0)
    
    # Conversion metrics
    inquiry_to_viewing_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    viewing_to_offer_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    offer_to_sale_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'performance_metrics'
        ordering = ['-date']
        verbose_name = 'Performance Metrics'
        verbose_name_plural = 'Performance Metrics'
    
    def __str__(self):
        return f"Performance Metrics - {self.date}"

class ReportSchedule(models.Model):
    """Scheduled reports for users."""
    
    REPORT_TYPES = (
        ('property_performance', 'Property Performance'),
        ('market_trends', 'Market Trends'),
        ('user_activity', 'User Activity'),
        ('revenue', 'Revenue Report'),
    )
    
    FREQUENCIES = (
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='report_schedules')
    report_type = models.CharField(max_length=30, choices=REPORT_TYPES)
    frequency = models.CharField(max_length=20, choices=FREQUENCIES)
    
    # Report configuration
    filters = models.JSONField(default=dict)
    email_recipients = models.JSONField(default=list)
    
    # Schedule settings
    is_active = models.BooleanField(default=True)
    next_run = models.DateTimeField()
    last_run = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'report_schedules'
        verbose_name = 'Report Schedule'
        verbose_name_plural = 'Report Schedules'
    
    def __str__(self):
        return f"{self.user.get_full_name()} - {self.get_report_type_display()} ({self.frequency})"
