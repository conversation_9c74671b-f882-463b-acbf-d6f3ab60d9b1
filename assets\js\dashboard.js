// ===== DASHBOARD PAGE FUNCTIONALITY =====

// Sample data for dashboard
const dashboardData = {
    userProperties: [
        {
            id: 1,
            title: "Luxury Penthouse Manhattan",
            location: "Upper East Side, New York",
            price: 8500000,
            status: "active",
            views: 342,
            listedDate: "2023-12-15",
            image: "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
        },
        {
            id: 2,
            title: "Modern Villa Beverly Hills",
            location: "Beverly Hills, California",
            price: 12000000,
            status: "active",
            views: 289,
            listedDate: "2023-11-28",
            image: "https://images.unsplash.com/photo-1600607687644-aac4c3eac7f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
        },
        {
            id: 3,
            title: "Waterfront Mansion Miami",
            location: "Miami Beach, Florida",
            price: 15500000,
            status: "pending",
            views: 421,
            listedDate: "2023-10-05",
            image: "https://images.unsplash.com/photo-1600607688960-e095ff8d5e6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
        },
        {
            id: 4,
            title: "Historic Brownstone Boston",
            location: "Back Bay, Boston",
            price: 4200000,
            status: "sold",
            views: 195,
            listedDate: "2023-09-12",
            image: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
        }
    ],
    recentActivity: [
        {
            type: "view",
            property: "Luxury Penthouse Manhattan",
            time: "2 hours ago",
            icon: "bi-eye"
        },
        {
            type: "inquiry",
            property: "Modern Villa Beverly Hills",
            time: "Yesterday",
            icon: "bi-chat-dots"
        },
        {
            type: "favorite",
            property: "Waterfront Mansion Miami",
            time: "2 days ago",
            icon: "bi-heart"
        },
        {
            type: "viewing",
            property: "Luxury Penthouse Manhattan",
            time: "3 days ago",
            icon: "bi-calendar-check"
        },
        {
            type: "offer",
            property: "Historic Brownstone Boston",
            time: "1 week ago",
            icon: "bi-cash-coin"
        }
    ],
    messages: [
        {
            id: 1,
            sender: "Sarah Johnson",
            subject: "Interest in Luxury Penthouse",
            message: "Hello, I'm interested in scheduling a viewing for the Luxury Penthouse in Manhattan. Is it possible to arrange a viewing this weekend?",
            date: "2023-12-20T14:30:00",
            read: false,
            avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
        },
        {
            id: 2,
            sender: "Michael Chen",
            subject: "Question about Modern Villa",
            message: "Hi there, I have a few questions about the Modern Villa in Beverly Hills. Specifically, I'd like to know more about the smart home features and security system.",
            date: "2023-12-19T09:15:00",
            read: false,
            avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
        },
        {
            id: 3,
            sender: "Emily Rodriguez",
            subject: "Offer for Waterfront Mansion",
            message: "I'd like to make an offer on the Waterfront Mansion in Miami. Please let me know the best way to proceed with this.",
            date: "2023-12-18T16:45:00",
            read: false,
            avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
        }
    ]
};

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    initializeTabs();
});

function loadDashboardData() {
    loadRecentProperties();
    loadRecentActivity();
    loadMyProperties();
    loadFavorites();
    loadMessages();
}

function initializeTabs() {
    const navLinks = document.querySelectorAll('.dashboard-nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            // Show corresponding tab content
            const tabId = this.getAttribute('data-tab');
            showTabContent(tabId);
        });
    });
}

function showTabContent(tabId) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.style.display = 'none';
    });
    
    // Show selected tab content
    const selectedContent = document.getElementById(`${tabId}-content`);
    if (selectedContent) {
        selectedContent.style.display = 'block';
    }
}

function loadRecentProperties() {
    const container = document.getElementById('recentPropertiesTable');
    if (!container) return;
    
    // Get 3 most recent properties
    const recentProperties = dashboardData.userProperties.slice(0, 3);
    
    container.innerHTML = recentProperties.map(property => `
        <tr>
            <td>
                <div class="d-flex align-items-center">
                    <img src="${property.image}" alt="${property.title}" class="property-image-thumb me-3">
                    <div>
                        <h6 class="mb-0">${property.title}</h6>
                        <small class="text-muted">${property.location}</small>
                    </div>
                </div>
            </td>
            <td>${formatPrice(property.price)}</td>
            <td><span class="status-badge ${property.status}">${capitalizeFirstLetter(property.status)}</span></td>
            <td>${property.views}</td>
            <td>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-secondary" title="Edit">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" title="View">
                        <i class="bi bi-eye"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function loadRecentActivity() {
    const container = document.getElementById('recentActivity');
    if (!container) return;
    
    container.innerHTML = dashboardData.recentActivity.map(activity => `
        <div class="activity-item">
            <div class="activity-icon">
                <i class="bi ${activity.icon}"></i>
            </div>
            <div class="activity-content">
                <p class="mb-1">
                    ${getActivityText(activity)}
                </p>
                <small class="activity-time">${activity.time}</small>
            </div>
        </div>
    `).join('');
}

function getActivityText(activity) {
    switch (activity.type) {
        case 'view':
            return `Someone viewed your <strong>${activity.property}</strong>`;
        case 'inquiry':
            return `You received an inquiry about <strong>${activity.property}</strong>`;
        case 'favorite':
            return `Someone added <strong>${activity.property}</strong> to favorites`;
        case 'viewing':
            return `Viewing scheduled for <strong>${activity.property}</strong>`;
        case 'offer':
            return `You received an offer for <strong>${activity.property}</strong>`;
        default:
            return `Activity related to <strong>${activity.property}</strong>`;
    }
}

function loadMyProperties() {
    const container = document.getElementById('myPropertiesTable');
    if (!container) return;
    
    container.innerHTML = dashboardData.userProperties.map(property => `
        <tr>
            <td>
                <div class="d-flex align-items-center">
                    <img src="${property.image}" alt="${property.title}" class="property-image-thumb me-3">
                    <div>
                        <h6 class="mb-0">${property.title}</h6>
                    </div>
                </div>
            </td>
            <td>${property.location}</td>
            <td>${formatPrice(property.price)}</td>
            <td><span class="status-badge ${property.status}">${capitalizeFirstLetter(property.status)}</span></td>
            <td>${property.views}</td>
            <td>${formatDate(property.listedDate)}</td>
            <td>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-secondary" title="Edit">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" title="View">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" title="Delete">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function loadFavorites() {
    // This would typically load from localStorage or API
    const favorites = getFavorites();
    
    // If we have property data available, we can display the favorites
    if (window.PropertyData && window.PropertyData.sampleProperties) {
        const favoriteProperties = window.PropertyData.sampleProperties.filter(
            property => favorites.includes(property.id.toString())
        );
        
        // This would be implemented in the favorites tab content
        console.log('Favorite properties:', favoriteProperties);
    }
}

function loadMessages() {
    // This would be implemented in the messages tab content
    console.log('Messages:', dashboardData.messages);
}

// Utility functions
function formatPrice(price) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(price);
}

function formatDate(dateString) {
    const date = new Date(dateString);

    // Check if the date is today
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
        return `Today, ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`;
    } else if (date.toDateString() === yesterday.toDateString()) {
        return `Yesterday, ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`;
    } else {
        return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
    }
}

function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}

function getFavorites() {
    return JSON.parse(localStorage.getItem('favorites') || '[]');
}

// Dashboard actions
function editProperty(propertyId) {
    window.location.href = `add-property.html?id=${propertyId}`;
}

function viewProperty(propertyId) {
    window.location.href = `property-detail.html?id=${propertyId}`;
}

function deleteProperty(propertyId) {
    if (confirm('Are you sure you want to delete this property? This action cannot be undone.')) {
        // This would typically make an API call
        console.log('Deleting property:', propertyId);
        
        // Show success message
        if (window.LuxEstate && window.LuxEstate.showToast) {
            window.LuxEstate.showToast('Property Deleted', 'The property has been successfully deleted.', 'success');
        }
        
        // Reload properties
        loadMyProperties();
    }
}

function markMessageAsRead(messageId) {
    // This would typically make an API call
    console.log('Marking message as read:', messageId);
    
    // Update local data
    const message = dashboardData.messages.find(m => m.id === messageId);
    if (message) {
        message.read = true;
    }
}

// Load favorites tab
function loadFavorites() {
    const favoritesGrid = document.getElementById('favoritesGrid');
    if (!favoritesGrid) return;

    const favorites = JSON.parse(localStorage.getItem('favorites') || '[]');

    if (favorites.length === 0) {
        favoritesGrid.innerHTML = `
            <div class="col-12 text-center py-5">
                <i class="bi bi-heart fs-1 text-muted mb-3"></i>
                <h5 class="text-muted">No favorite properties yet</h5>
                <p class="text-muted">Start browsing properties and add them to your favorites!</p>
                <a href="listings.html" class="btn btn-warning">Browse Properties</a>
            </div>
        `;
        return;
    }

    // Get property data for favorites
    const favoriteProperties = [];
    if (window.PropertyData && window.PropertyData.sampleProperties) {
        favorites.forEach(favId => {
            const property = window.PropertyData.sampleProperties.find(p => p.id.toString() === favId);
            if (property) favoriteProperties.push(property);
        });
    }

    favoritesGrid.innerHTML = favoriteProperties.map(property => `
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card property-card h-100">
                <div class="property-image">
                    <img src="${property.image}" alt="${property.title}" class="card-img-top">
                    <div class="property-badge">${property.badge}</div>
                    <div class="listing-type-badge ${property.listingType}">${property.listingType === 'rent' ? 'For Rent' : 'For Sale'}</div>
                </div>
                <div class="card-body">
                    <div class="property-price">${formatPrice(property.listingType === 'rent' ? property.rentPrice : property.price)}${property.listingType === 'rent' ? '/month' : ''}</div>
                    <h6 class="card-title">${property.title}</h6>
                    <p class="property-location text-muted">
                        <i class="bi bi-geo-alt me-1"></i>${property.location}
                    </p>
                    <div class="d-flex gap-2">
                        <a href="property-detail.html?id=${property.id}" class="btn btn-warning btn-sm flex-fill">View</a>
                        <button class="btn btn-outline-danger btn-sm" onclick="removeFromFavorites('${property.id}')">
                            <i class="bi bi-heart-fill"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// Load messages tab
function loadMessages() {
    const messagesList = document.getElementById('messagesList');
    if (!messagesList) return;

    messagesList.innerHTML = dashboardData.messages.map(message => `
        <div class="message-item p-3 border-bottom ${message.read ? '' : 'bg-light'}" onclick="selectMessage(${message.id})">
            <div class="d-flex align-items-center">
                <div class="message-avatar me-3">
                    <img src="${message.avatar}" alt="${message.sender}" class="rounded-circle" width="40" height="40" onerror="this.outerHTML='<i class=\\'bi bi-person-circle fs-4 text-muted\\'></i>'">
                </div>
                <div class="flex-grow-1">
                    <h6 class="mb-1">${message.sender}</h6>
                    <p class="mb-1 text-muted small">${message.subject}</p>
                    <small class="text-muted">${formatDate(message.date)}</small>
                </div>
                ${!message.read ? '<div class="badge bg-warning">New</div>' : ''}
            </div>
        </div>
    `).join('');
}

// Load analytics tab
function loadAnalytics() {
    const topProperties = document.getElementById('topProperties');
    if (!topProperties) return;

    const topPerforming = dashboardData.userProperties.slice(0, 3);

    topProperties.innerHTML = topPerforming.map((property, index) => `
        <div class="d-flex align-items-center mb-3 ${index < topPerforming.length - 1 ? 'border-bottom pb-3' : ''}">
            <div class="me-3">
                <span class="badge bg-warning">#${index + 1}</span>
            </div>
            <div class="flex-grow-1">
                <h6 class="mb-1">${property.title}</h6>
                <small class="text-muted">${property.views} views</small>
            </div>
            <div class="text-end">
                <div class="fw-bold">${formatPrice(property.price)}</div>
                <small class="text-success">+${Math.floor(Math.random() * 20 + 5)}%</small>
            </div>
        </div>
    `).join('');
}

// Message functions
function selectMessage(messageId) {
    const message = dashboardData.messages.find(m => m.id === messageId);
    if (!message) return;

    const messageThread = document.getElementById('messageThread');
    if (messageThread) {
        messageThread.innerHTML = `
            <div class="message-header mb-3">
                <h6 class="fw-bold">${message.subject}</h6>
                <small class="text-muted">From: ${message.sender} • ${formatDate(message.date)}</small>
            </div>
            <div class="message-body">
                <p>${message.message}</p>
            </div>
        `;
    }

    // Mark as read
    markMessageAsRead(messageId);
    loadMessages(); // Refresh the list
}

function composeMessage() {
    if (window.LuxEstate && window.LuxEstate.showToast) {
        window.LuxEstate.showToast('Compose Message', 'Message composition feature coming soon!', 'info');
    }
}

function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    if (messageInput && messageInput.value.trim()) {
        if (window.LuxEstate && window.LuxEstate.showToast) {
            window.LuxEstate.showToast('Message Sent', 'Your message has been sent successfully!', 'success');
        }
        messageInput.value = '';
    }
}

function removeFromFavorites(propertyId) {
    let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    favorites = favorites.filter(id => id !== propertyId.toString());
    localStorage.setItem('favorites', JSON.stringify(favorites));

    if (window.LuxEstate && window.LuxEstate.showToast) {
        window.LuxEstate.showToast('Removed', 'Property removed from favorites.', 'info');
    }

    loadFavorites(); // Refresh the favorites display
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Load initial data
    loadRecentProperties();
    loadRecentActivity();
    loadMyProperties();
    loadFavorites();
    loadMessages();
    loadAnalytics();

    // Bind tab switching events
    document.querySelectorAll('.dashboard-nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const tabName = this.getAttribute('data-tab');
            switchTab(tabName);
        });
    });
});

function switchTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.style.display = 'none';
    });

    // Remove active class from all nav links
    document.querySelectorAll('.dashboard-nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // Show selected tab content
    const selectedTab = document.getElementById(`${tabName}-content`);
    if (selectedTab) {
        selectedTab.style.display = 'block';
    }

    // Add active class to selected nav link
    const selectedLink = document.querySelector(`[data-tab="${tabName}"]`);
    if (selectedLink) {
        selectedLink.classList.add('active');
    }

    // Reload data for specific tabs
    if (tabName === 'favorites') {
        loadFavorites();
    } else if (tabName === 'messages') {
        loadMessages();
    } else if (tabName === 'analytics') {
        loadAnalytics();
    }
}

// Make functions globally available
window.Dashboard = {
    editProperty,
    viewProperty,
    deleteProperty,
    markMessageAsRead,
    selectMessage,
    composeMessage,
    sendMessage,
    removeFromFavorites,
    switchTab
};

// Make global functions available for HTML onclick handlers
window.composeMessage = composeMessage;
window.sendMessage = sendMessage;
window.selectMessage = selectMessage;
