// ===== LISTINGS PAGE FUNCTIONALITY =====

let currentProperties = [];
let filteredProperties = [];
let currentPage = 1;
const propertiesPerPage = 9;
let currentFilters = {};
let currentSort = 'price-desc';
let currentView = 'grid';

// Initialize listings page
document.addEventListener('DOMContentLoaded', function() {
    initializeListingsPage();
    bindListingsEvents();
    loadPropertiesFromURL();
});

function initializeListingsPage() {
    // Load all properties
    currentProperties = window.PropertyData ? window.PropertyData.sampleProperties : [];
    filteredProperties = [...currentProperties];
    
    // Apply initial sort
    sortProperties(currentSort);
    
    // Render properties
    renderProperties();
    updateResultsCount();
}

function bindListingsEvents() {
    // Filter form submission
    const filterForm = document.querySelector('.filter-form');
    if (filterForm) {
        filterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            applyFilters();
        });
        
        filterForm.addEventListener('reset', function() {
            setTimeout(() => {
                currentFilters = {};
                applyFilters();
            }, 100);
        });
        
        // Real-time filtering on input change
        filterForm.addEventListener('input', debounce(applyFilters, 500));
        filterForm.addEventListener('change', applyFilters);
    }
    
    // Sort dropdown
    const sortSelect = document.querySelector('.sort-select');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            currentSort = this.value;
            sortProperties(currentSort);
            renderProperties();
        });
    }
    
    // View toggle buttons
    document.querySelectorAll('[data-view]').forEach(btn => {
        btn.addEventListener('click', function() {
            const view = this.getAttribute('data-view');
            switchView(view);
        });
    });
    
    // Price filter quick buttons
    document.querySelectorAll('.price-filter').forEach(btn => {
        btn.addEventListener('click', function() {
            const min = this.getAttribute('data-min');
            const max = this.getAttribute('data-max');
            
            const minInput = document.querySelector('input[name="minPrice"]');
            const maxInput = document.querySelector('input[name="maxPrice"]');
            
            if (minInput) minInput.value = min || '';
            if (maxInput) maxInput.value = max || '';
            
            applyFilters();
        });
    });
    
    // Load more button
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreProperties);
    }
}

function loadPropertiesFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    
    // Apply URL parameters to filters
    const filters = {};
    for (const [key, value] of urlParams.entries()) {
        if (value) {
            filters[key] = value;
            
            // Set form values
            const input = document.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = value;
            }
        }
    }
    
    if (Object.keys(filters).length > 0) {
        currentFilters = filters;
        applyFilters();
    }
}

function applyFilters() {
    const formData = new FormData(document.querySelector('.filter-form'));
    currentFilters = {};
    
    // Collect form data
    for (const [key, value] of formData.entries()) {
        if (value.trim()) {
            if (key === 'features') {
                if (!currentFilters.features) currentFilters.features = [];
                currentFilters.features.push(value);
            } else {
                currentFilters[key] = value;
            }
        }
    }
    
    // Filter properties
    filteredProperties = filterPropertiesArray(currentProperties, currentFilters);
    
    // Sort filtered properties
    sortProperties(currentSort);
    
    // Reset pagination
    currentPage = 1;
    
    // Render results
    renderProperties();
    updateResultsCount();
    
    // Update URL
    updateURL();
}

function filterPropertiesArray(properties, filters) {
    return properties.filter(property => {
        // Location filter
        if (filters.location) {
            const locationMatch = property.location.toLowerCase().includes(filters.location.toLowerCase()) ||
                                property.title.toLowerCase().includes(filters.location.toLowerCase());
            if (!locationMatch) return false;
        }
        
        // Listing type filter
        if (filters.listingType) {
            if (property.listingType !== filters.listingType) return false;
        }

        // Property type filter
        if (filters.type) {
            if (property.type.toLowerCase() !== filters.type.toLowerCase()) return false;
        }

        // Bedrooms filter
        if (filters.bedrooms) {
            if (property.bedrooms < parseInt(filters.bedrooms)) return false;
        }
        
        // Price range filter
        if (filters.minPrice) {
            if (property.price < parseInt(filters.minPrice)) return false;
        }
        
        if (filters.maxPrice) {
            if (property.price > parseInt(filters.maxPrice)) return false;
        }
        
        // Features filter
        if (filters.features && filters.features.length > 0) {
            const hasAllFeatures = filters.features.every(feature => 
                property.features.some(propFeature => 
                    propFeature.toLowerCase().includes(feature.toLowerCase())
                )
            );
            if (!hasAllFeatures) return false;
        }
        
        return true;
    });
}

function sortProperties(sortBy) {
    switch (sortBy) {
        case 'price-asc':
            filteredProperties.sort((a, b) => a.price - b.price);
            break;
        case 'price-desc':
            filteredProperties.sort((a, b) => b.price - a.price);
            break;
        case 'bedrooms-desc':
            filteredProperties.sort((a, b) => b.bedrooms - a.bedrooms);
            break;
        case 'area-desc':
            filteredProperties.sort((a, b) => b.area - a.area);
            break;
        case 'newest':
            filteredProperties.sort((a, b) => b.id - a.id);
            break;
        default:
            break;
    }
}

function renderProperties() {
    const container = document.getElementById('propertyGrid');
    if (!container) return;
    
    const startIndex = 0;
    const endIndex = currentPage * propertiesPerPage;
    const propertiesToShow = filteredProperties.slice(startIndex, endIndex);
    
    if (propertiesToShow.length === 0) {
        container.innerHTML = `
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="bi bi-house-x display-1 text-muted mb-3"></i>
                    <h4>No Properties Found</h4>
                    <p class="text-muted">Try adjusting your filters to see more results.</p>
                    <button class="btn btn-warning" onclick="clearAllFilters()">Clear All Filters</button>
                </div>
            </div>
        `;
        return;
    }
    
    if (currentView === 'grid') {
        container.className = 'property-grid row g-4';
        container.innerHTML = propertiesToShow.map(property => 
            `<div class="col-lg-4 col-md-6">${createPropertyCard(property)}</div>`
        ).join('');
    } else {
        container.className = 'property-list';
        container.innerHTML = propertiesToShow.map(property => 
            createPropertyListItem(property)
        ).join('');
    }
    
    // Update load more button
    updateLoadMoreButton();
}

function createPropertyCard(property) {
    const favorites = getFavorites();
    const isFavorite = favorites.includes(property.id.toString());

    // Determine price display based on listing type
    let priceDisplay = '';
    if (property.listingType === 'rent') {
        priceDisplay = `${window.PropertyData ? window.PropertyData.formatPrice(property.rentPrice) : property.rentPrice}/month`;
        if (property.price) {
            priceDisplay += ` <small class="text-muted">(${window.PropertyData ? window.PropertyData.formatPrice(property.price) : property.price} to buy)</small>`;
        }
    } else {
        priceDisplay = window.PropertyData ? window.PropertyData.formatPrice(property.price) : property.price;
    }

    // Determine listing type badge
    const listingBadge = property.listingType === 'rent' ? 'For Rent' : 'For Sale';

    return `
        <div class="card property-card h-100">
            <div class="property-image">
                <img src="${property.image}" alt="${property.title}" class="card-img-top">
                <div class="property-badge">${property.badge}</div>
                <div class="listing-type-badge ${property.listingType}">${listingBadge}</div>
                <button class="favorite-btn ${isFavorite ? 'active' : ''}" data-property-id="${property.id}">
                    <i class="bi bi-heart${isFavorite ? '-fill' : ''}"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="property-price">${priceDisplay}</div>
                <h5 class="card-title">${property.title}</h5>
                <p class="property-location">
                    <i class="bi bi-geo-alt me-1"></i>${property.location}
                </p>
                <div class="property-features">
                    <span class="property-feature">
                        <i class="bi bi-house me-1"></i>${property.bedrooms} Beds
                    </span>
                    <span class="property-feature">
                        <i class="bi bi-droplet me-1"></i>${property.bathrooms} Baths
                    </span>
                    <span class="property-feature">
                        <i class="bi bi-arrows-angle-expand me-1"></i>${window.PropertyData ? window.PropertyData.formatNumber(property.area) : property.area} sqft
                    </span>
                </div>
                <div class="d-flex gap-2 mt-3">
                    <a href="property-detail.html?id=${property.id}" class="btn btn-warning flex-fill">View Details</a>
                    <button class="btn btn-outline-warning" onclick="scheduleViewing(${property.id})">
                        <i class="bi bi-calendar"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
}

function createPropertyListItem(property) {
    const favorites = getFavorites();
    const isFavorite = favorites.includes(property.id.toString());

    // Determine price display based on listing type
    let priceDisplay = '';
    if (property.listingType === 'rent') {
        priceDisplay = `${window.PropertyData ? window.PropertyData.formatPrice(property.rentPrice) : property.rentPrice}/month`;
        if (property.price) {
            priceDisplay += ` <small class="text-muted">(${window.PropertyData ? window.PropertyData.formatPrice(property.price) : property.price} to buy)</small>`;
        }
    } else {
        priceDisplay = window.PropertyData ? window.PropertyData.formatPrice(property.price) : property.price;
    }

    // Determine listing type badge
    const listingBadge = property.listingType === 'rent' ? 'For Rent' : 'For Sale';

    return `
        <div class="card property-card mb-4">
            <div class="row g-0">
                <div class="col-md-4">
                    <div class="property-image" style="height: 250px;">
                        <img src="${property.image}" alt="${property.title}" class="img-fluid h-100 w-100" style="object-fit: cover;">
                        <div class="property-badge">${property.badge}</div>
                        <div class="listing-type-badge ${property.listingType}">${listingBadge}</div>
                        <button class="favorite-btn ${isFavorite ? 'active' : ''}" data-property-id="${property.id}">
                            <i class="bi bi-heart${isFavorite ? '-fill' : ''}"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="card-body h-100 d-flex flex-column">
                        <div class="property-price">${priceDisplay}</div>
                        <h5 class="card-title">${property.title}</h5>
                        <p class="property-location">
                            <i class="bi bi-geo-alt me-1"></i>${property.location}
                        </p>
                        <p class="text-muted flex-grow-1">${property.description}</p>
                        <div class="property-features mb-3">
                            <span class="property-feature">
                                <i class="bi bi-house me-1"></i>${property.bedrooms} Beds
                            </span>
                            <span class="property-feature">
                                <i class="bi bi-droplet me-1"></i>${property.bathrooms} Baths
                            </span>
                            <span class="property-feature">
                                <i class="bi bi-arrows-angle-expand me-1"></i>${window.PropertyData ? window.PropertyData.formatNumber(property.area) : property.area} sqft
                            </span>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="property-detail.html?id=${property.id}" class="btn btn-warning">View Details</a>
                            <button class="btn btn-outline-warning" onclick="scheduleViewing(${property.id})">
                                <i class="bi bi-calendar me-1"></i>Schedule Viewing
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function switchView(view) {
    currentView = view;
    
    // Update button states
    document.querySelectorAll('[data-view]').forEach(btn => {
        btn.classList.toggle('active', btn.getAttribute('data-view') === view);
    });
    
    // Re-render properties
    renderProperties();
}

function loadMoreProperties() {
    currentPage++;
    renderProperties();
}

function updateLoadMoreButton() {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (!loadMoreBtn) return;
    
    const totalShown = currentPage * propertiesPerPage;
    const hasMore = totalShown < filteredProperties.length;
    
    if (hasMore) {
        loadMoreBtn.style.display = 'block';
        loadMoreBtn.innerHTML = `Load More Properties (${filteredProperties.length - totalShown} remaining)`;
    } else {
        loadMoreBtn.style.display = 'none';
    }
}

function updateResultsCount() {
    const countElement = document.getElementById('resultsCount');
    if (countElement) {
        countElement.textContent = filteredProperties.length;
    }
}

function updateURL() {
    const params = new URLSearchParams();
    
    for (const [key, value] of Object.entries(currentFilters)) {
        if (Array.isArray(value)) {
            value.forEach(v => params.append(key, v));
        } else {
            params.set(key, value);
        }
    }
    
    const newURL = `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`;
    window.history.replaceState({}, '', newURL);
}

function clearAllFilters() {
    document.querySelector('.filter-form').reset();
    currentFilters = {};
    applyFilters();
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function getFavorites() {
    return window.LuxEstate ? window.LuxEstate.getFavorites() : JSON.parse(localStorage.getItem('favorites') || '[]');
}

// Make functions globally available
window.ListingsPage = {
    applyFilters,
    sortProperties,
    switchView,
    clearAllFilters,
    loadMoreProperties
};
