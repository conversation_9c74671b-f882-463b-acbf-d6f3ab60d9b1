# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 04:22-0500\n"
"PO-Revision-Date: 2020-06-14 17:00-0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: fa\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.7.4\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: account/adapter.py:48
msgid "Username can not be used. Please use other username."
msgstr "نام‌کاربری قابل استفاده نیست. لطفا نام‌کاربری دیگری استفاده کن."

#: account/adapter.py:54
msgid "Too many failed login attempts. Try again later."
msgstr "خیلی زیاد تلاش ناموفق کردی، لطفا بعدا ازنو سعی کن."

#: account/adapter.py:56
msgid "A user is already registered with this email address."
msgstr "یک کاربر ازقبل با این نشانی رایانامه ثبت شده."

#: account/adapter.py:57
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "گذرواژه کنونی"

#: account/adapter.py:308
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "گذرواژه باید حداقل {0} کاراکتر باشد."

#: account/apps.py:9
#, fuzzy
msgid "Accounts"
msgstr "حساب‌ها"

#: account/forms.py:58 account/forms.py:432
msgid "You must type the same password each time."
msgstr "هربار باید گذرواژه‌ی یکسانی وارد کنی."

#: account/forms.py:90 account/forms.py:397 account/forms.py:522
#: account/forms.py:658
msgid "Password"
msgstr "گذرواژه"

#: account/forms.py:91
msgid "Remember Me"
msgstr "مرا به یادآور"

#: account/forms.py:95
msgid "This account is currently inactive."
msgstr "اکنون این حساب غیرفعال است."

#: account/forms.py:97
msgid "The email address and/or password you specified are not correct."
msgstr "نشانی رایانامه یا گذرواژه نادرست است."

#: account/forms.py:100
msgid "The username and/or password you specified are not correct."
msgstr "نام‌کاربری یا گذرواژه نادرست است."

#: account/forms.py:111 account/forms.py:271 account/forms.py:459
#: account/forms.py:540
msgid "Email address"
msgstr "نشانی رایانامه"

#: account/forms.py:115 account/forms.py:316 account/forms.py:456
#: account/forms.py:535
msgid "Email"
msgstr "رایانامه"

#: account/forms.py:118 account/forms.py:121 account/forms.py:261
#: account/forms.py:264
msgid "Username"
msgstr "نام‌کاربری"

#: account/forms.py:131
msgid "Username or email"
msgstr "نام‌کاربری ویا رایانامه"

#: account/forms.py:134
msgctxt "field label"
msgid "Login"
msgstr "ورود"

#: account/forms.py:307
#, fuzzy
#| msgid "Email (optional)"
msgid "Email (again)"
msgstr "رایانامه (ازنو)"

#: account/forms.py:311
#, fuzzy
#| msgid "email confirmation"
msgid "Email address confirmation"
msgstr "تاییدیه‌ی نشانی رایانامه"

#: account/forms.py:319
msgid "Email (optional)"
msgstr "رایانامه (اختیاری)"

#: account/forms.py:368
#, fuzzy
#| msgid "You must type the same password each time."
msgid "You must type the same email each time."
msgstr "هربار باید رایانامه‌ی یکسانی وارد کنی."

#: account/forms.py:401 account/forms.py:523
msgid "Password (again)"
msgstr "گذرواژه (ازنو)"

#: account/forms.py:470
#, fuzzy
#| msgid "This email address is already associated with another account."
msgid "This email address is already associated with this account."
msgstr "این نشانی رایانامه ازقبل به این حساب وصل شده."

#: account/forms.py:472
#, fuzzy, python-format
#| msgid "Your account has no verified email address."
msgid "You cannot add more than %d email addresses."
msgstr "حساب‌ات هیچ رایانامه‌ي تایید‌شده‌ای ندارد."

#: account/forms.py:503
msgid "Current Password"
msgstr "گذرواژه کنونی"

#: account/forms.py:505 account/forms.py:607
msgid "New Password"
msgstr "گذرواژه جدید"

#: account/forms.py:506 account/forms.py:608
msgid "New Password (again)"
msgstr "گذرواژه جدید (ازنو)"

#: account/forms.py:514
msgid "Please type your current password."
msgstr "لطفا گذرواژه کنونی‌‌ات را وارد کن."

#: account/forms.py:552
msgid "The email address is not assigned to any user account"
msgstr "این نشانی رایانامه به هیچ حساب کاربری‌ای منتسب نشده."

#: account/forms.py:628
msgid "The password reset token was invalid."
msgstr "توکن بازنشانی گذرواژه نامعتبر است."

#: account/models.py:21
msgid "user"
msgstr "کاربر"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "نشانی رایانامه"

#: account/models.py:28
msgid "verified"
msgstr "تاییدشده"

#: account/models.py:29
msgid "primary"
msgstr "اصلی"

#: account/models.py:35
msgid "email addresses"
msgstr "نشانی‌های رایانامه"

#: account/models.py:141
msgid "created"
msgstr "ایجاد‌شده"

#: account/models.py:142
msgid "sent"
msgstr "ارسال شد"

#: account/models.py:143 socialaccount/models.py:62
msgid "key"
msgstr "کلید"

#: account/models.py:148
msgid "email confirmation"
msgstr "تاییدیه‌ی رایانامه"

#: account/models.py:149
msgid "email confirmations"
msgstr "تاییدیه‌های رایانامه"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:12
msgid "Code"
msgstr ""

#: mfa/forms.py:29
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:30
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"یه حساب‌کاربری با این نشانی رایانامه وجود دارد. لطفا نخست وارد آن شو، سپس "
"حساب %s ات را بهش وصل کن."

#: socialaccount/adapter.py:136
msgid "Your account has no password set up."
msgstr "هیچ گذرواژه‌ای برای حساب‌ات نهاده نشده."

#: socialaccount/adapter.py:143
msgid "Your account has no verified email address."
msgstr "حساب‌ات هیچ رایانامه‌ي تایید‌شده‌ای ندارد."

#: socialaccount/apps.py:7
#, fuzzy
msgid "Social Accounts"
msgstr "حساب‌های اجتماعی"

#: socialaccount/models.py:36 socialaccount/models.py:97
msgid "provider"
msgstr "فراهم‌کننده"

#: socialaccount/models.py:45
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "فراهم‌کننده"

#: socialaccount/models.py:49
#, fuzzy
msgid "name"
msgstr "نام"

#: socialaccount/models.py:51
msgid "client id"
msgstr "شناسه مشتری"

#: socialaccount/models.py:53
msgid "App ID, or consumer key"
msgstr "شناسه اپ، یا کلید مصرف‌کننده"

#: socialaccount/models.py:56
msgid "secret key"
msgstr "کلید محرمانه"

#: socialaccount/models.py:59
msgid "API secret, client secret, or consumer secret"
msgstr "رمز رابک (رابط برنامه‌ی کاربردی API)، رمز مشتری، یا رمز مصرف‌کننده"

#: socialaccount/models.py:62
#, fuzzy
msgid "Key"
msgstr "کلید"

#: socialaccount/models.py:81
msgid "social application"
msgstr "اپلیکیشن اجتماعی"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "اپلیکیشن‌های اجتماعی"

#: socialaccount/models.py:117
msgid "uid"
msgstr "شناسه‌کاربری"

#: socialaccount/models.py:119
msgid "last login"
msgstr "آخرین ورود"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "تاریخ پیوست‌شده"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "داده اضافی"

#: socialaccount/models.py:125
msgid "social account"
msgstr "حساب اجتماعی"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "حساب‌های اجتماعی"

#: socialaccount/models.py:160
msgid "token"
msgstr "توکن"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) یا توکن دسترسی (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "رمز توکن"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr "\"oauth_token_secret\" (OAuth1) یا توکن تازه‌سازی (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "انقضا"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "توکن اپلیکشن اجتماعی"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "توکن‌های اپلیکیشن اجتماعی"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr "داده نامعتبر نمایه"

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "پاسخ نامعتبر هنگام دریافت توکن درخواست از \"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "پاسخ نامعتبر هنگام دریافت توکن دسترسی از \"%s\""

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "توکن درخواست‌ْای برای \"%s\" ذخیره نشده."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "توکن دسترسی‌ای برای \"%s\" ذخیره نشده."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "دسترسی به منابع خصوصی \"%s\" وجود ندارد."

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "پاسخ نامعتبر هنگام دریافت توکن درخواست از \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:8
msgid "Account Inactive"
msgstr "حساب غیرفعال"

#: templates/account/account_inactive.html:10
msgid "This account is inactive."
msgstr "این حساب‌کاربری غیرفعال است."

#: templates/account/base.html:16
msgid "Messages:"
msgstr ""

#: templates/account/base.html:26
msgid "Menu:"
msgstr ""

#: templates/account/base.html:29 templates/account/email_change.html:31
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "رایانامه"

#: templates/account/base.html:30 templates/account/logout.html:5
#: templates/account/logout.html:8 templates/account/logout.html:17
msgid "Sign Out"
msgstr "خروج"

#: templates/account/base.html:32 templates/account/login.html:6
#: templates/account/login.html:10 templates/account/login.html:43
#: templates/mfa/authenticate.html:4 templates/mfa/authenticate.html:16
#: templates/socialaccount/login.html:4
msgid "Sign In"
msgstr "ورود"

#: templates/account/base.html:33 templates/account/signup.html:8
#: templates/account/signup.html:18 templates/socialaccount/signup.html:8
#: templates/socialaccount/signup.html:19
msgid "Sign Up"
msgstr "ثبت نام"

#: templates/account/email.html:5 templates/account/email.html:8
msgid "Email Addresses"
msgstr "نشانی‌های رایانامه"

#: templates/account/email.html:10
msgid "The following email addresses are associated with your account:"
msgstr "نشانی‌های رایانامه زیر به حساب‌ات متصل شده‌اند:"

#: templates/account/email.html:24
msgid "Verified"
msgstr "تایید‌شده"

#: templates/account/email.html:26
msgid "Unverified"
msgstr "تایید‌نشده"

#: templates/account/email.html:28
msgid "Primary"
msgstr "اصلی"

#: templates/account/email.html:34
msgid "Make Primary"
msgstr "اصلی کردن"

#: templates/account/email.html:35 templates/account/email_change.html:21
msgid "Re-send Verification"
msgstr "بازارسال تاییدیه"

#: templates/account/email.html:36 templates/socialaccount/connections.html:35
msgid "Remove"
msgstr "حذف"

#: templates/account/email.html:47
msgid "Add Email Address"
msgstr "افزودن نشانی رایانامه"

#: templates/account/email.html:52
msgid "Add Email"
msgstr "افزودن رایانامه"

#: templates/account/email.html:62
msgid "Do you really want to remove the selected email address?"
msgstr "واقعا می‌خواهی نشانی رایانامه‌ی انتخاب‌شده را حذف کنی؟"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, fuzzy, python-format
#| msgid ""
#| "Thank you from %(site_name)s!\n"
#| "%(site_domain)s"
msgid "Hello from %(site_name)s!"
msgstr ""
"سپاسگزارت %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"سپاس‌گزاریم برای استفاده از %(site_name)s.\n"
"‏%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because user %(user_display)s at "
#| "%(site_domain)s has given yours as an e-mail address to connect their "
#| "account.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s\n"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"سلام،\n"
"\n"
"این رایانامه را دریافت کردی چون کاربر %(user_display)s نشانی رایانامه‌ات را "
"برای اتصال به حساب‌اش در %(site_name)s داده.\n"
"\n"
"برای تایید درستی این به ‌%(activate_url)s برو.\n"

#: templates/account/email/email_confirmation_subject.txt:3
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Please Confirm Your Email Address"
msgstr "تایید نشانی رایانامه"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"سلام،\n"
"\n"
"این رایانامه را دریافت کرده‌ای چون برای حساب کاربری‌ات در %(site_name)s،  از "
"جانب خودت یا کسی دیگر، یه گذرواژه درخواست شده.\n"
"برای بازنشانی گذرواژه پیوند زیر را دنبال کن. وگرنه چشم‌پوشی از این هنگامی که "
"خودت هم درخواست نکردی می‌تواند امن باشد."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "در صورت فراموشی، نام‌کاربری‌ات %(username)s است."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "رایانامه‌ی بازنشانی گذرواژه"

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"سلام،\n"
"\n"
"این رایانامه را دریافت کرده‌ای چون برای حساب کاربری‌ات در %(site_name)s،  از "
"جانب خودت یا کسی دیگر، یه گذرواژه درخواست شده.\n"
"برای بازنشانی گذرواژه پیوند زیر را دنبال کن. وگرنه چشم‌پوشی از این هنگامی که "
"خودت هم درخواست نکردی می‌تواند امن باشد."

#: templates/account/email_change.html:4 templates/account/email_change.html:7
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "نشانی‌های رایانامه"

#: templates/account/email_change.html:11
#, fuzzy
#| msgid "The following email addresses are associated with your account:"
msgid "The following email address is associated with your account:"
msgstr "نشانی‌های رایانامه زیر به حساب‌ات متصل شده‌اند:"

#: templates/account/email_change.html:16
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification:"
msgstr "نشانی رایانامه‌ی اصلی‌ات باید تاییدشده باشد."

#: templates/account/email_change.html:27
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Change Email Address"
msgstr "تایید نشانی رایانامه"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "تایید نشانی رایانامه"

#: templates/account/email_confirm.html:17
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"لطفا تایید کن که <a href=\"mailto:%(email)s\">%(email)s</a> یه نشانی "
"رایانامه برای کاربر %(user_display)s است."

#: templates/account/email_confirm.html:21
#: templates/account/reauthenticate.html:19
msgid "Confirm"
msgstr "تایید"

#: templates/account/email_confirm.html:24
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "حساب اجتماعی‌اِ به یه حساب دیگر متصل شده."

#: templates/account/email_confirm.html:31
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a href="
#| "\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"این پیوند تاییدیه رایانامه باطل‌شده ویا نامعتبر است. لطفا یه <a href="
"\"%(email_url)s\">تاییدیه جدید رایانامه</a> درخواست کن."

#: templates/account/login.html:15
#, python-format
msgid ""
"Please sign in with one\n"
"of your existing third party accounts. Or, <a href=\"%(signup_url)s\">sign "
"up</a>\n"
"for a %(site_name)s account and sign in below:"
msgstr ""
"لطفا با یکی از حساب‌های شخص سوم موجودت وارد شو. یا در %(site_name)s<a href="
"\"%(signup_url)s\">ثبت‌نام</a> کن و از زیر وارد شو:"

#: templates/account/login.html:25
msgid "or"
msgstr "یا"

#: templates/account/login.html:32
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"<a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"اگر هنوز یه حساب نساختی، پس لطفا نخست <a href=\"%(signup_url)s\">ثبت‌نام</a> "
"کن."

#: templates/account/login.html:42 templates/account/password_change.html:14
msgid "Forgot Password?"
msgstr "گذرواژه‌ات را فراموش کرده‌ای؟"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "مطمئنی می‌خواهی خارج شوی؟"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "نمی‌توانی نشانی رایانامه‌ی اصلی‌ات (%(email)s) را حذف کنی."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "رایانامه‌ی تاییدیه به %(email)s فرستاده شد."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "نشانی رایانامه‌ %(email)s را تایید کرده‌ای."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "نشانی رایانامه %(email)s حذف شد."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "با %(name)s باموفقیت وارد شدی."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "خارج شده‌ای."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "گذرواژه باموفقیت تغییر کرد."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "گذرواژه باموفقیت نهاده شد."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "نشانی رانامه اصلی نهاده شد."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "نشانی رایانامه‌ی اصلی‌ات باید تاییدشده باشد."

#: templates/account/password_change.html:5
#: templates/account/password_change.html:8
#: templates/account/password_change.html:13
#: templates/account/password_reset_from_key.html:4
#: templates/account/password_reset_from_key.html:7
#: templates/account/password_reset_from_key_done.html:4
#: templates/account/password_reset_from_key_done.html:7
msgid "Change Password"
msgstr "تغییر گذرواژه"

#: templates/account/password_reset.html:6
#: templates/account/password_reset.html:10
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:9
msgid "Password Reset"
msgstr "بازنشانی گذرواژه"

#: templates/account/password_reset.html:15
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"گذرواژه‌ات را فراموش کرده‌ای؟ نشانی رایانامه‌ات را در زیر درج کن؛ ما رایانامه‌ی "
"بازنشانی گذرواژه را برایت خواهیم فرستاد."

#: templates/account/password_reset.html:20
msgid "Reset My Password"
msgstr "درخواست بازنشانی"

#: templates/account/password_reset.html:23
msgid "Please contact us if you have any trouble resetting your password."
msgstr "اگر مشکلی در بازنشانی گذرواژه‌ات داری، لطفا با ما تماس بگیر."

#: templates/account/password_reset_done.html:15
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"ما یه رایانامه تاییدیه بهت فرستادیم؛ لطفا روی پیوند درونش کلیک کن. اگر تا "
"دقایقی دیگر دریافتش نکردی با ما تماس بگیر."

#: templates/account/password_reset_from_key.html:7
msgid "Bad Token"
msgstr "توکن نادرست"

#: templates/account/password_reset_from_key.html:11
#, fuzzy, python-format
#| msgid ""
#| "The password reset link was invalid, possibly because it has already been "
#| "used.  Please request a <a href=\"%(passwd_reset_url)s\">new password "
#| "reset</a>."
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"پیوند بازنشانی گذرواژه نادرست است؛ شاید قبلا استفاده شده. لطفا یه <a class="
"\"alert-link\" href=\"{{ passwd_reset_url }}\">بازنشان جدید گذرواژه</"
"a>درخواست کن."

#: templates/account/password_reset_from_key.html:16
msgid "change password"
msgstr "تغییر گذرواژه"

#: templates/account/password_reset_from_key_done.html:8
msgid "Your password is now changed."
msgstr "گذرواژه‌ات اکنون تغییر کرد."

#: templates/account/password_set.html:5 templates/account/password_set.html:8
#: templates/account/password_set.html:13
msgid "Set Password"
msgstr "نهادن گذرواژه"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "تایید نشانی رایانامه"

#: templates/account/reauthenticate.html:11
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""

#: templates/account/signup.html:5 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "ثبت‌نام"

#: templates/account/signup.html:10
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "ازقبل یه حساب داری؟ پس لطفا <a href=\"%(login_url)s\">ورود</a> کن."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:8
msgid "Sign Up Closed"
msgstr "توقف ثبت‌نام"

#: templates/account/signup_closed.html:10
msgid "We are sorry, but the sign up is currently closed."
msgstr "متاسفیم، ولی اکنون ثبت‌نام متوقف شده."

#: templates/account/snippets/already_logged_in.html:5
msgid "Note"
msgstr "توجه"

#: templates/account/snippets/already_logged_in.html:5
#, python-format
msgid "you are already logged in as %(user_display)s."
msgstr "ازقبل با %(user_display)s وارد شده‌ای."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "هشدار:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"اکنون هیچ نشانی رایانامه‌ی نهادیده‌ای نداری. درواقع باید یه نشانی رایانامه "
"بافزایی تا بتوانی اعلان‌ها، بازنشان گذرواژه و غیره را دریافت کنی."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:8
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:8
msgid "Verify Your Email Address"
msgstr "تایید نشانی رایانامه"

#: templates/account/verification_sent.html:10
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. Please contact us if you do not receive "
#| "it within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"یه رایانامه تاییدیه بهت فرستادیم. پیوند درونش را برای کامل کردن فرایند "
"ثبت‌نام دنبال کن. اگر تا چند دقیقه‌ی دیگر دریافتش نکردی، لطفا با ما تماس بگیر."

#: templates/account/verified_email_required.html:12
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"این بخش از سایت نیاز دارد تایید کنیم شما کسی که ادعا کرده‌‌ای هستی.برای همین، "
"نیازد داریم مالکیتت بر نشانی رایانامه‌ات را تایید کنی."

#: templates/account/verified_email_required.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"ما یه رایانامه تاییدیه بهت فرستادیم؛ لطفا روی پیوند درونش کلیک کن. اگر تا "
"دقایقی دیگر دریافتش نکردی با ما تماس بگیر."

#: templates/account/verified_email_required.html:20
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>توجه:</strong> هنوز می‌توانی <a href=\"%(email_url)s\">تعویض نشانی "
"رایانامه</a> کنی."

#: templates/mfa/authenticate.html:7 templates/mfa/index.html:4
#: templates/mfa/index.html:7
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:9
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:9 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:11
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/deactivate_form.html:11
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:18
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:21 templates/mfa/totp/activate_form.html:16
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:4
#: templates/mfa/recovery_codes/index.html:4
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:30 templates/mfa/recovery_codes/index.html:6
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""

#: templates/mfa/index.html:34
msgid "View codes"
msgstr ""

#: templates/mfa/index.html:37 templates/mfa/recovery_codes/index.html:16
msgid "Download codes"
msgstr ""

#: templates/mfa/index.html:40 templates/mfa/recovery_codes/index.html:20
msgid "Generate new codes"
msgstr ""

#: templates/mfa/index.html:44
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:6
msgid ""
"You are about to generate a new set of recovery codes for your account. This "
"action will invalidate your existing codes. Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "Generate"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:9
#, fuzzy
#| msgid "token secret"
msgid "Authenticator secret"
msgstr "رمز توکن"

#: templates/mfa/totp/deactivate_form.html:4
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:6
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/openid/login.html:9
msgid "OpenID Sign In"
msgstr "ورودبا OpenID"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:8
msgid "Social Network Login Failure"
msgstr "ناموفق در ورود با شبکه اجتماعی"

#: templates/socialaccount/authentication_error.html:10
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr "یه خطا هنگام سعی برای ورود با حساب شبکه‌ی اجتماعی‌ات رخ داد."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:8
msgid "Account Connections"
msgstr "اتصال‌های حساب"

#: templates/socialaccount/connections.html:11
msgid ""
"You can sign in to your account using any of the following third party "
"accounts:"
msgstr "با هریک از حساب‌های شخص سوم زیر می‌توانی به حساب‌ات وارد شوی:"

#: templates/socialaccount/connections.html:43
msgid ""
"You currently have no social network accounts connected to this account."
msgstr "اکنون هیچ حساب شبکه‌ی اجتماعی‌ای به این حساب متصل نشده."

#: templates/socialaccount/connections.html:46
msgid "Add a 3rd Party Account"
msgstr "افزودن یه حساب سوم شخص"

#: templates/socialaccount/login.html:8
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:10
#, python-format
msgid "You are about to connect a new third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:12
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:14
#, python-format
msgid "You are about to sign in using a third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:19
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "لغو ورود"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"ورودت به سایت‌مان با یکی از حساب‌هایت را لغو کردی. اگر اشتباهی شده، لطفا اقدام "
"به <a href=\"%(login_url)s\">ورود</a> کن."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "حساب اجتماعی‌اِ متصل شد."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "حساب اجتماعی‌اِ به یه حساب دیگر متصل شده."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "حساب اجتماعی‌اِ قطع‌ارتباط شد."

#: templates/socialaccount/signup.html:10
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"چند قدمی ورود به %(site_name)s با حساب‌ات %(provider_name)s هستی. در گام آخر، "
"لطفا فرم زیر را کامل کن:"

#~ msgid "This email address is already associated with another account."
#~ msgstr "این نشانی رایانامه ازقبل به حساب دیگری وصل شده."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "بهت یه رایانامه فرستادیم. اگر تا چند دقیقه‌ی دیگر دریافتش نکردی باهامون "
#~ "تماس بگیر."

#~ msgid "Account"
#~ msgstr "حساب"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "اطلاعات داده شده درست نیست."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "نام‌کاربری تنها می‌تواند شامل حروف، اعداد، و  @/./+/-/_. باشد"

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "این نام‌کاربری قبلا گرفته شده. لطفا یکی دیگر انتخاب کن."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "ورود با Shopify"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "تایید کرده‌ای که <a href=\"mailto:%(email)s\">%(email)s</a> یه نشانی "
#~ "رایانامه برای کاربر %(user_display)s است."
