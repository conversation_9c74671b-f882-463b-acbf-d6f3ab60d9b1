#!/usr/bin/env python3
"""
Complete LuxEstate Project Setup Script
This script sets up both frontend and backend with full integration.
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path

def run_command(command, description, cwd=None):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True,
            cwd=cwd
        )
        print(f"✅ {description} completed successfully!")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during {description}:")
        print(f"Command: {command}")
        print(f"Error: {e.stderr}")
        return None

def check_requirements():
    """Check system requirements."""
    print("🔍 Checking system requirements...")
    
    # Check Python
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python 3.8 or higher is required!")
        sys.exit(1)
    print(f"✅ Python {python_version.major}.{python_version.minor} detected")
    
    # Check if we're in the right directory
    if not os.path.exists('requirements.txt'):
        print("❌ requirements.txt not found. Please run this script from the project root.")
        sys.exit(1)
    
    return True

def setup_virtual_environment():
    """Set up Python virtual environment."""
    print("\n📦 Setting up virtual environment...")
    
    venv_name = "luxestate_env"
    
    if os.path.exists(venv_name):
        print(f"📁 Virtual environment '{venv_name}' already exists")
        response = input("Do you want to recreate it? (y/N): ").strip().lower()
        if response == 'y':
            shutil.rmtree(venv_name)
            run_command(f"python -m venv {venv_name}", f"Creating virtual environment '{venv_name}'")
        else:
            print("Using existing virtual environment")
    else:
        run_command(f"python -m venv {venv_name}", f"Creating virtual environment '{venv_name}'")
    
    # Determine activation command based on OS
    if platform.system() == "Windows":
        activate_cmd = f"{venv_name}\\Scripts\\activate"
        pip_cmd = f"{venv_name}\\Scripts\\pip"
        python_cmd = f"{venv_name}\\Scripts\\python"
    else:
        activate_cmd = f"source {venv_name}/bin/activate"
        pip_cmd = f"{venv_name}/bin/pip"
        python_cmd = f"{venv_name}/bin/python"
    
    # Install requirements
    run_command(f"{pip_cmd} install --upgrade pip", "Upgrading pip")
    run_command(f"{pip_cmd} install -r requirements.txt", "Installing Django and dependencies")
    
    return python_cmd, activate_cmd, pip_cmd

def setup_django_project(python_cmd):
    """Set up Django project."""
    print("\n🏗️ Setting up Django project...")
    
    # Create .env file
    if not os.path.exists('.env'):
        print("📝 Creating .env file...")
        env_content = """# Django Settings
SECRET_KEY=django-insecure-luxestate-development-key-change-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (SQLite for development)
DB_ENGINE=django.db.backends.sqlite3
DB_NAME=db.sqlite3

# Email Settings (Console backend for development)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# Celery Settings
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Stripe Settings (Add your keys)
STRIPE_PUBLISHABLE_KEY=pk_test_your_key_here
STRIPE_SECRET_KEY=sk_test_your_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
"""
        with open('.env', 'w') as f:
            f.write(env_content)
        print("✅ .env file created")
    
    # Run Django setup commands
    run_command(f"{python_cmd} manage.py makemigrations", "Creating database migrations")
    run_command(f"{python_cmd} manage.py migrate", "Running database migrations")
    
    # Create directories
    os.makedirs('static/assets/css', exist_ok=True)
    os.makedirs('static/assets/js', exist_ok=True)
    os.makedirs('static/assets/images', exist_ok=True)
    os.makedirs('media', exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    # Copy frontend files to Django structure
    setup_frontend_integration()
    
    # Collect static files
    run_command(f"{python_cmd} manage.py collectstatic --noinput", "Collecting static files")

def setup_frontend_integration():
    """Integrate frontend files with Django."""
    print("\n🎨 Setting up frontend integration...")
    
    # Copy HTML files to templates
    html_files = [
        'index.html', 'listings.html', 'property-detail.html',
        'dashboard.html', 'favorites.html', 'add-property.html',
        'about.html', 'contact.html', 'blog.html', 'blog-detail.html'
    ]
    
    for html_file in html_files:
        if os.path.exists(html_file):
            shutil.copy2(html_file, 'templates/')
            print(f"📄 Copied {html_file} to templates/")
    
    # Copy assets to static directory
    if os.path.exists('assets'):
        if os.path.exists('static/assets'):
            shutil.rmtree('static/assets')
        shutil.copytree('assets', 'static/assets')
        print("📁 Copied assets to static/assets/")

def create_sample_data(python_cmd):
    """Create sample data for development."""
    print("\n📊 Creating sample data...")
    
    # Create sample data script
    sample_data_script = """
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'luxestate_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from properties.models import Property, PropertyCategory
from decimal import Decimal

User = get_user_model()

# Create sample users
if not User.objects.filter(username='admin').exists():
    admin_user = User.objects.create_superuser(
        username='admin',
        email='<EMAIL>',
        password='admin123',
        first_name='Admin',
        last_name='User',
        user_type='admin'
    )
    print("✅ Admin user created (username: admin, password: admin123)")

if not User.objects.filter(username='agent').exists():
    agent_user = User.objects.create_user(
        username='agent',
        email='<EMAIL>',
        password='agent123',
        first_name='John',
        last_name='Agent',
        user_type='agent'
    )
    print("✅ Agent user created (username: agent, password: agent123)")

# Create property categories
categories = [
    {'name': 'Residential', 'slug': 'residential'},
    {'name': 'Commercial', 'slug': 'commercial'},
    {'name': 'Luxury', 'slug': 'luxury'},
]

for cat_data in categories:
    category, created = PropertyCategory.objects.get_or_create(
        slug=cat_data['slug'],
        defaults=cat_data
    )
    if created:
        print(f"✅ Created category: {category.name}")

print("🎉 Sample data created successfully!")
"""
    
    with open('create_sample_data.py', 'w') as f:
        f.write(sample_data_script)
    
    run_command(f"{python_cmd} create_sample_data.py", "Creating sample data")
    os.remove('create_sample_data.py')

def display_final_instructions(activate_cmd):
    """Display final setup instructions."""
    print("\n" + "="*60)
    print("🎉 LuxEstate Setup Complete!")
    print("="*60)
    
    print("\n📋 Next Steps:")
    print(f"1. Activate virtual environment: {activate_cmd}")
    print("2. Start Django development server: python manage.py runserver")
    print("3. Visit your application:")
    print("   • Frontend: http://127.0.0.1:8000/")
    print("   • Admin Panel: http://127.0.0.1:8000/admin/")
    print("   • API Root: http://127.0.0.1:8000/api/")
    
    print("\n👤 Default Login Credentials:")
    print("   • Admin: username=admin, password=admin123")
    print("   • Agent: username=agent, password=agent123")
    
    print("\n🔧 Optional Services:")
    print("   • Install Redis: Required for real-time features")
    print("   • Start Celery worker: celery -A luxestate_backend worker -l info")
    print("   • Start Celery beat: celery -A luxestate_backend beat -l info")
    
    print("\n📚 Documentation:")
    print("   • Django Admin: Full backend management")
    print("   • API Endpoints: RESTful API for frontend integration")
    print("   • WebSocket Support: Real-time messaging")
    
    print("\n🚀 Happy coding!")
    print("="*60)

def main():
    """Main setup function."""
    print("🏠 LuxEstate - Complete Project Setup")
    print("=" * 50)
    
    try:
        # Check requirements
        check_requirements()
        
        # Setup virtual environment
        python_cmd, activate_cmd, pip_cmd = setup_virtual_environment()
        
        # Setup Django project
        setup_django_project(python_cmd)
        
        # Create sample data
        create_sample_data(python_cmd)
        
        # Display final instructions
        display_final_instructions(activate_cmd)
        
    except KeyboardInterrupt:
        print("\n\n❌ Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Setup failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
